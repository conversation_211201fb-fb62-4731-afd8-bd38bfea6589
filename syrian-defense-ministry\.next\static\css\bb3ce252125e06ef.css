/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */@import "https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap";@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,::backdrop,:after,:before{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-duration:initial;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-space-x-reverse:0}}}.visible{visibility:visible}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.top-1\/2{top:50%}.top-full{top:100%}.right-1\/2{right:50%}.left-1\/2{left:50%}.z-10{z-index:10}.z-50{z-index:50}.mx-auto{margin-inline:auto}.mr-auto{margin-right:auto}.ml-auto{margin-left:auto}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-flex{display:inline-flex}.h-full{height:100%}.min-h-screen{min-height:100vh}.w-full{width:100%}.max-w-none{max-width:none}.flex-1{flex:1}.flex-shrink-0{flex-shrink:0}.origin-top{transform-origin:top}.-translate-x-1\/2{--tw-translate-x:calc(calc(1/2*100%)*-1)}.-translate-x-1\/2,.-translate-y-1\/2{translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y:calc(calc(1/2*100%)*-1)}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.truncate{text-overflow:ellipsis;white-space:nowrap}.overflow-hidden,.truncate{overflow:hidden}.overflow-y-auto{overflow-y:auto}.rounded-full{border-radius:3.40282e+38px}.border{border-style:var(--tw-border-style);border-width:1px}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-4{border-style:var(--tw-border-style);border-width:4px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-pure-white{border-color:var(--pure-white)}.border-smoke-gray\/30{border-color:var(--smoke-gray)}@supports (color:color-mix(in lab,red,red)){.border-smoke-gray\/30{border-color:color-mix(in oklab,var(--smoke-gray)30%,transparent)}}.border-steel-gray,.border-steel-gray\/30{border-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.border-steel-gray\/30{border-color:color-mix(in oklab,var(--steel-gray)30%,transparent)}}.border-warning-red\/30{border-color:var(--warning-red)}@supports (color:color-mix(in lab,red,red)){.border-warning-red\/30{border-color:color-mix(in oklab,var(--warning-red)30%,transparent)}}.bg-bright-green\/10{background-color:var(--bright-green)}@supports (color:color-mix(in lab,red,red)){.bg-bright-green\/10{background-color:color-mix(in oklab,var(--bright-green)10%,transparent)}}.bg-bright-green\/20{background-color:var(--bright-green)}@supports (color:color-mix(in lab,red,red)){.bg-bright-green\/20{background-color:color-mix(in oklab,var(--bright-green)20%,transparent)}}.bg-deep-charcoal{background-color:var(--deep-charcoal)}.bg-military-black,.bg-military-black\/30{background-color:var(--military-black)}@supports (color:color-mix(in lab,red,red)){.bg-military-black\/30{background-color:color-mix(in oklab,var(--military-black)30%,transparent)}}.bg-military-black\/50{background-color:var(--military-black)}@supports (color:color-mix(in lab,red,red)){.bg-military-black\/50{background-color:color-mix(in oklab,var(--military-black)50%,transparent)}}.bg-military-black\/70{background-color:var(--military-black)}@supports (color:color-mix(in lab,red,red)){.bg-military-black\/70{background-color:color-mix(in oklab,var(--military-black)70%,transparent)}}.bg-military-black\/90{background-color:var(--military-black)}@supports (color:color-mix(in lab,red,red)){.bg-military-black\/90{background-color:color-mix(in oklab,var(--military-black)90%,transparent)}}.bg-military-green,.bg-military-green\/10{background-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.bg-military-green\/10{background-color:color-mix(in oklab,var(--military-green)10%,transparent)}}.bg-military-green\/20{background-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.bg-military-green\/20{background-color:color-mix(in oklab,var(--military-green)20%,transparent)}}.bg-military-green\/50{background-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.bg-military-green\/50{background-color:color-mix(in oklab,var(--military-green)50%,transparent)}}.bg-military-green\/90{background-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.bg-military-green\/90{background-color:color-mix(in oklab,var(--military-green)90%,transparent)}}.bg-pure-white{background-color:var(--pure-white)}.bg-smoke-gray,.bg-smoke-gray\/30{background-color:var(--smoke-gray)}@supports (color:color-mix(in lab,red,red)){.bg-smoke-gray\/30{background-color:color-mix(in oklab,var(--smoke-gray)30%,transparent)}}.bg-smoke-gray\/50{background-color:var(--smoke-gray)}@supports (color:color-mix(in lab,red,red)){.bg-smoke-gray\/50{background-color:color-mix(in oklab,var(--smoke-gray)50%,transparent)}}.bg-steel-gray,.bg-steel-gray\/20{background-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.bg-steel-gray\/20{background-color:color-mix(in oklab,var(--steel-gray)20%,transparent)}}.bg-steel-gray\/30{background-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.bg-steel-gray\/30{background-color:color-mix(in oklab,var(--steel-gray)30%,transparent)}}.bg-steel-gray\/50{background-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.bg-steel-gray\/50{background-color:color-mix(in oklab,var(--steel-gray)50%,transparent)}}.bg-steel-gray\/70{background-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.bg-steel-gray\/70{background-color:color-mix(in oklab,var(--steel-gray)70%,transparent)}}.bg-steel-gray\/95{background-color:var(--steel-gray)}@supports (color:color-mix(in lab,red,red)){.bg-steel-gray\/95{background-color:color-mix(in oklab,var(--steel-gray)95%,transparent)}}.bg-transparent{background-color:#0000}.bg-warning-red,.bg-warning-red\/20{background-color:var(--warning-red)}@supports (color:color-mix(in lab,red,red)){.bg-warning-red\/20{background-color:color-mix(in oklab,var(--warning-red)20%,transparent)}}.bg-gradient-to-b{--tw-gradient-position:to bottom in oklab}.bg-gradient-to-b,.bg-gradient-to-br{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab}.bg-gradient-to-r{--tw-gradient-position:to right in oklab}.bg-gradient-to-r,.bg-gradient-to-t{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-t{--tw-gradient-position:to top in oklab}.from-deep-charcoal{--tw-gradient-from:var(--deep-charcoal);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-military-black{--tw-gradient-from:var(--military-black);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-military-black\/50{--tw-gradient-from:var(--military-black)}@supports (color:color-mix(in lab,red,red)){.from-military-black\/50{--tw-gradient-from:color-mix(in oklab,var(--military-black)50%,transparent)}}.from-military-black\/50{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-military-green{--tw-gradient-from:var(--military-green);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-pure-white{--tw-gradient-from:var(--pure-white);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-smoke-gray{--tw-gradient-from:var(--smoke-gray);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-steel-gray{--tw-gradient-from:var(--steel-gray);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-bright-green{--tw-gradient-via:var(--bright-green);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.via-deep-charcoal{--tw-gradient-via:var(--deep-charcoal);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.via-light-gray{--tw-gradient-via:var(--light-gray);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-bright-green{--tw-gradient-to:var(--bright-green);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-military-green{--tw-gradient-to:var(--military-green);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-pure-white{--tw-gradient-to:var(--pure-white);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-smoke-gray{--tw-gradient-to:var(--smoke-gray);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-steel-gray{--tw-gradient-to:var(--steel-gray);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-transparent{--tw-gradient-to:transparent;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.break-words{overflow-wrap:break-word}.text-bright-green{color:var(--bright-green)}.text-light-gray{color:var(--light-gray)}.text-military-black{color:var(--military-black)}.text-military-green{color:var(--military-green)}.text-muted-gray{color:var(--muted-gray)}.text-pure-white{color:var(--pure-white)}.text-transparent{color:#0000}.text-warning-red{color:var(--warning-red)}.placeholder-light-gray::placeholder{color:var(--light-gray)}.opacity-30{opacity:.3}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}@media (hover:hover){.group-hover\:text-bright-green:is(:where(.group):hover *){color:var(--bright-green)}.hover\:scale-105:hover{--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}.hover\:border-bright-green:hover{border-color:var(--bright-green)}.hover\:bg-bright-green:hover,.hover\:bg-bright-green\/50:hover{background-color:var(--bright-green)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-bright-green\/50:hover{background-color:color-mix(in oklab,var(--bright-green)50%,transparent)}}.hover\:bg-military-green:hover,.hover\:bg-military-green\/70:hover{background-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-military-green\/70:hover{background-color:color-mix(in oklab,var(--military-green)70%,transparent)}}.hover\:bg-smoke-gray:hover,.hover\:bg-smoke-gray\/30:hover{background-color:var(--smoke-gray)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-smoke-gray\/30:hover{background-color:color-mix(in oklab,var(--smoke-gray)30%,transparent)}}.hover\:bg-warning-red\/80:hover{background-color:var(--warning-red)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-warning-red\/80:hover{background-color:color-mix(in oklab,var(--warning-red)80%,transparent)}}.hover\:text-bright-green:hover{color:var(--bright-green)}.hover\:text-pure-white:hover{color:var(--pure-white)}}.focus\:border-military-green:focus{border-color:var(--military-green)}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-military-green\/20:focus{--tw-ring-color:var(--military-green)}@supports (color:color-mix(in lab,red,red)){.focus\:ring-military-green\/20:focus{--tw-ring-color:color-mix(in oklab,var(--military-green)20%,transparent)}}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.rtl\:right-auto:where(:dir(rtl),[dir=rtl],[dir=rtl] *){right:auto}.rtl\:left-auto:where(:dir(rtl),[dir=rtl],[dir=rtl] *){left:auto}:where(.rtl\:space-x-reverse:where(:dir(rtl),[dir=rtl],[dir=rtl] *)>:not(:last-child)){--tw-space-x-reverse:1}.rtl\:text-left:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:left}.rtl\:text-right:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}:root{--deep-charcoal:#1a1a1a;--military-black:#0d0d0d;--steel-gray:#2d2d2d;--smoke-gray:#404040;--military-green:#2d5016;--bright-green:#4a7c59;--syrian-flag-green:#007a3d;--warning-red:#8b0000;--pure-white:#fff;--light-gray:#ccc;--muted-gray:#999;--background:var(--deep-charcoal);--foreground:var(--pure-white);--font-arabic-primary:"Amiri",serif;--font-arabic-secondary:"Noto Sans Arabic",sans-serif;--font-arabic-headings:"Cairo",sans-serif;--font-english-primary:"Inter",sans-serif;--font-english-secondary:"Inter",sans-serif;--font-mono:"JetBrains Mono",monospace}*{box-sizing:border-box;margin:0;padding:0}html{scroll-behavior:smooth}body{background:var(--background);color:var(--foreground);font-family:var(--font-english-primary);line-height:1.6;overflow-x:hidden}[dir=rtl]{font-family:var(--font-arabic-primary)}[dir=rtl] h1,[dir=rtl] h2,[dir=rtl] h3,[dir=rtl] h4,[dir=rtl] h5,[dir=rtl] h6{font-family:var(--font-arabic-headings)}.camo-pattern{background-image:radial-gradient(circle at 20%,#2d2d2d1a 0,#0000 50%),radial-gradient(circle at 40% 20%,#40404014 0,#0000 50%),radial-gradient(circle at 80% 80%,#1d1d1d1a 0,#0000 50%),radial-gradient(circle at 60% 60%,#2d50160d 0,#0000 50%);background-position:0 0,50px 50px,100px 0,150px 100px;background-size:200px 200px,150px 150px,180px 180px,220px 220px}.military-glow{box-shadow:0 0 20px #2d50164d}.military-glow-strong{box-shadow:0 0 30px #2d501680,0 0 60px #2d501633}.transition-all{transition:all .3s ease-in-out}.transition-fast{transition:all .15s ease-out}.transition-slow{transition:all .5s ease-in-out}::-webkit-scrollbar{width:8px}::-webkit-scrollbar-track{background:var(--military-black)}::-webkit-scrollbar-thumb{background:var(--military-green);border-radius:4px}::-webkit-scrollbar-thumb:hover{background:var(--bright-green)}.line-clamp-2{-webkit-line-clamp:2}.line-clamp-2,.line-clamp-3{-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-3{-webkit-line-clamp:3}@media (max-width:768px){.camo-pattern{background-size:100px 100px,75px 75px,90px 90px,110px 110px}}a:focus-visible,button:focus-visible,input:focus-visible,select:focus-visible{outline:2px solid var(--military-green);outline-offset:2px}@keyframes pulse-glow{0%,to{box-shadow:0 0 20px #2d50164d}50%{box-shadow:0 0 30px #2d501699}}.pulse-glow{animation:pulse-glow 2s ease-in-out infinite}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}