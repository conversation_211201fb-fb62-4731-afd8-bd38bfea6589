"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[500],{760:(t,e,i)=>{i.d(e,{N:()=>v});var n=i(5155),r=i(2115),s=i(869),o=i(2885),a=i(7494),l=i(845),u=i(7351),h=i(1508);class c extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:i,anchorX:s}=t,o=(0,r.useId)(),a=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:r,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:a,sizeRef:l,children:r.cloneElement(e,{ref:a})})}let p=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:p}=t,f=(0,o.M)(m),g=(0,r.useId)(),y=!0,v=(0,r.useMemo)(()=>(y=!1,{id:g,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(f.set(t,!0),f.values()))if(!e)return;a&&a()},register:t=>(f.set(t,!1),()=>f.delete(t))}),[s,f,a]);return h&&y&&(v={...v}),(0,r.useMemo)(()=>{f.forEach((t,e)=>f.set(e,!1))},[s]),r.useEffect(()=>{s||f.size||!a||a()},[s]),"popLayout"===c&&(e=(0,n.jsx)(d,{isPresent:s,anchorX:p,children:e})),(0,n.jsx)(l.t.Provider,{value:v,children:e})};function m(){return new Map}var f=i(2082);let g=t=>t.key||"";function y(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:d=!1,anchorX:m="left"}=t,[v,x]=(0,f.xQ)(d),w=(0,r.useMemo)(()=>y(e),[e]),b=d&&!v?[]:w.map(g),T=(0,r.useRef)(!0),P=(0,r.useRef)(w),S=(0,o.M)(()=>new Map),[A,E]=(0,r.useState)(w),[M,k]=(0,r.useState)(w);(0,a.E)(()=>{T.current=!1,P.current=w;for(let t=0;t<M.length;t++){let e=g(M[t]);b.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[M,b.length,b.join("-")]);let C=[];if(w!==A){let t=[...w];for(let e=0;e<M.length;e++){let i=M[e],n=g(i);b.includes(n)||(t.splice(e,0,i),C.push(i))}return"wait"===c&&C.length&&(t=C),k(y(t)),E(w),null}let{forceRender:V}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:M.map(t=>{let e=g(t),r=(!d||!!v)&&(w===M||b.includes(e));return(0,n.jsx)(p,{isPresent:r,initial:(!T.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:c,onExitComplete:r?void 0:()=>{if(!S.has(e))return;S.set(e,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(null==V||V(),k(P.current),d&&(null==x||x()),u&&u())},anchorX:m,children:t},e)})})}},845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2198:(t,e,i)=>{i.d(e,{K:()=>n});function n(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let r=i?.[t]??n.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}},2664:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let n=i(9991),r=i(7102);function s(t){if(!(0,n.isAbsoluteUrl)(t))return!0;try{let e=(0,n.getLocationOrigin)(),i=new URL(t,e);return i.origin===e&&(0,r.hasBasePath)(i.pathname)}catch(t){return!1}}},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let n=i(6966)._(i(8859)),r=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||r.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2771:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},3418:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))})},4105:(t,e,i)=>{let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sk});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],d={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&d.value&&d.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=o,v=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),p.process(r),m.process(r),f.process(r),g.process(r),y.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(v))},x=()=>{i=!0,n=!0,r.isProcessing||t(v)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:m,cancel:f,state:g,steps:y}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),w=new Set(["width","height","top","left","right","bottom",...v]);function b(t,e){-1===t.indexOf(e)&&t.push(e)}function T(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class P{constructor(){this.subscriptions=[]}add(t){return b(this.subscriptions,t),()=>T(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){n=void 0}let A={now:()=>(void 0===n&&A.set(g.isProcessing||h.useManualTiming?g.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(S)}},E=t=>!isNaN(parseFloat(t)),M={current:void 0};class k{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new P);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return M.current&&M.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function C(t,e){return new k(t,e)}let V=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),O=(t,e)=>i=>e(t(i)),F=(...t)=>t.reduce(O),B=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,U=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},W=()=>{},$=()=>{},_=t=>e=>"string"==typeof e&&e.startsWith(t),z=_("--"),K=_("var(--"),Y=t=>!!K(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,H={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},q={...H,transform:t=>B(0,1,t)},G={...H,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>B(0,255,t),tn={...H,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Z(q.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),td={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(q.transform(n))+")"},tm={test:t=>tr.test(t)||ts.test(t)||tp.test(t),parse:t=>tr.test(t)?tr.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tm.parse(t);return e.alpha=0,tm.transform(e)}},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tv,t=>(tm.test(t)?(n.color.push(s),r.push(ty),i.push(tm.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tw(t){return tx(t).values}function tb(t){let{split:e,types:i}=tx(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tg?r+=Z(t[s]):e===ty?r+=tm.transform(t[s]):r+=t[s]}return r}}let tT=t=>"number"==typeof t?0:tm.test(t)?tm.getAnimatableNone(t):t,tP={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tw,createTransformer:tb,getAnimatableNone:function(t){let e=tw(t);return tb(t)(e.map(tT))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tM=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tk=[ts,tr,tp],tC=t=>tk.find(e=>e.test(t));function tV(t){let e=tC(t);if(W(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tS(a,n,t+1/3),s=tS(a,n,t),o=tS(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tD=(t,e)=>{let i=tV(t),n=tV(e);if(!i||!n)return tA(t,e);let r={...i};return t=>(r.red=tM(i.red,n.red,t),r.green=tM(i.green,n.green,t),r.blue=tM(i.blue,n.blue,t),r.alpha=tE(i.alpha,n.alpha,t),tr.transform(r))},tR=new Set(["none","hidden"]);function tj(t,e){return i=>tE(t,e,i)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?Y(t)?tA:tm.test(t)?tD:tB:Array.isArray(t)?tO:"object"==typeof t?tm.test(t)?tD:tF:tA}function tO(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tF(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tL(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tB=(t,e)=>{let i=tP.createTransformer(e),n=tx(t),r=tx(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tR.has(t)&&!r.values.length||tR.has(e)&&!n.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):F(tO(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(W(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tL(t)(t,e)}let tU=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>g.isProcessing?g.timestamp:A.now()}},tN=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tW(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function t$(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let t_={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tK=["duration","bounce"],tY=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function tH(t=t_.visualDuration,e=t_.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:t_.velocity,stiffness:t_.stiffness,damping:t_.damping,mass:t_.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tY)&&tX(t,tK))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:t_.mass,stiffness:n,damping:r}}else{let i=function({duration:t=t_.duration,bounce:e=t_.bounce,velocity:i=t_.velocity,mass:n=t_.mass}){let r,s;W(t<=I(t_.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=B(t_.minDamping,t_.maxDamping,o),t=B(t_.minDuration,t_.maxDuration,U(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tz(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tz(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=I(t),isNaN(a))return{stiffness:t_.stiffness,damping:t_.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:t_.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-U(n.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*c)),y=a-o,v=U(Math.sqrt(u/c)),x=5>Math.abs(y);if(r||(r=x?t_.restSpeed.granular:t_.restSpeed.default),s||(s=x?t_.restDelta.granular:t_.restDelta.default),g<1){let t=tz(v,g);i=e=>a-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),n=Math.min(t*e,300);return a-i*((f+g*v*y)*Math.sinh(n)+t*y*Math.cosh(n))/t}}let w={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let n=0===t?f:0;g<1&&(n=0===t?I(f):t$(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tW(w),2e4),e=tN(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tq({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,y=i*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let w=t=>-y*Math.exp(-t/n),b=t=>x+w(t),T=t=>{let e=w(t),i=b(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},P=t=>{f(m.value)&&(c=t,d=tH({keyframes:[m.value,g(m.value)],velocity:t$(b,t,m.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,T(t),P(t)),void 0!==c&&t>=c)?d.next(t-c):(e||T(t),m)}}}tH.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tW(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:U(r)}}(t,100,tH);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tG(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tG(r(t),e,n)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t9=t5(t3),t4=t2(t9),t6=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t5(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t9,backInOut:t4,backOut:t3,anticipate:t6},en=t=>"string"==typeof t,er=t=>{if(ee(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tZ(e,i,n,r)}return en(t)?($(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=F(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>c(B(t[0],t[s-1],e)):c}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tE(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tq,inertia:tq,tween:eo,keyframes:eo,spring:tH};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ed=t=>t/100;class ep extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=F(ed,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tW(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/o)):"mirror"===c&&(x=s)),v=B(0,1,i)*o}let w=y?{done:!1,value:u[0]}:x.next(v);r&&(w.value=r(w.value));let{done:b}=w;y||null===a||(b=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return T&&p!==tq&&(w.value=el(u,this.options,f,this.speed)),m&&m(w.value),T&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tU,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>ey(em(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>ey(em(Math.atan2(t[6],t[5]))),rotateY:t=>ey(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eb(t){return+!!t.includes("scale")}function eT(t,e){let i,n;if(!t||"none"===t)return eb(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ew,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return eb(e);let s=i[e],o=n[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}let eP=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eT(i,e)};function eS(t){return parseFloat(t.trim())}let eA=t=>t===H||t===tu,eE=new Set(["x","y","z"]),eM=v.filter(t=>!eE.has(t)),ek={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eT(e,"x"),y:(t,{transform:e})=>eT(e,"y")};ek.translateX=ek.x,ek.translateY=ek.y;let eC=new Set,eV=!1,eD=!1,eR=!1;function ej(){if(eD){let t=Array.from(eC).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eM.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eV=!1,eC.forEach(t=>t.complete(eR)),eC.clear()}function eL(){eC.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eO{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eC.add(this),eV||(eV=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eC.delete(this)}cancel(){"scheduled"===this.state&&(eC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eF=t=>t.startsWith("--");function eB(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eB(()=>void 0!==window.ScrollTimeline),eU={},eN=function(t,e){let i=eB(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eW=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,e$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eW([0,.65,.55,1]),circOut:eW([.55,0,1,.45]),backIn:eW([.31,.01,.66,-.59]),backOut:eW([.33,1.53,.69,.99])};function e_(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e_(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?eW(e):Array.isArray(e)?e.map(e=>t(e,i)||e$.easeOut):e$[e]}(a,r);Array.isArray(c)&&(h.easing=c),d.value&&N.waapi++;let p={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return d.value&&m.finished.finally(()=>{N.waapi--}),m}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eF(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eK={anticipate:t6,backInOut:t4,circInOut:et};class eY extends ez{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eK&&(t.ease=eK[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tP.test(t)||"0"===t)&&!t.startsWith("url("));var eH,eq,eG=i(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eB(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||eO;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=A.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eX(r,e),a=eX(s,e);return W(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e_(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(d)?new eY({...d,element:d.motionValue.owner.current}):new ep(d);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,eL(),ej(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e9=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e2(e[1]):e1:e3,e4=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=I(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(d,e9(t,d)),d.duration&&(d.duration=I(d.duration)),d.repeatDelay&&(d.repeatDelay=I(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,a);if(void 0!==t)return void m.update(()=>{d.onUpdate(t),d.onComplete()})}return a.isSync?new ep(d):new eJ(d)};function e6(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,d=!0)}}R(t,e),n.start(e4(e,n,r,t.shouldReduceMotion&&w.has(e)?{type:!1}:o,t,d));let p=n.animation;p&&h.push(p)}return o&&Promise.all(h).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=V(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,C(i))}}(t,o)})}),h}function e8(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e6(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e7).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e8(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e8(t,e,i)));else if("string"==typeof e)n=e8(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e6(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,d={},p=1/0;for(let e=0;e<ia;e++){var m,f;let a=io[e],g=i[a],y=void 0!==l[a]?l[a]:u[a],v=ie(y),x=a===o?g.isActive:null;!1===x&&(p=e);let w=y===u[a]&&y!==l[a]&&v;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...d},!g.isActive&&null===x||!y&&!g.prevProp||r(y)||"boolean"==typeof y)continue;let b=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!it(f,m)),T=b||a===o&&g.isActive&&!w&&v||e>p&&v,P=!1,S=Array.isArray(y)?y:[y],A=S.reduce(s(a),{});!1===x&&(A={});let{prevResolvedValues:E={}}=g,M={...E,...A},k=e=>{T=!0,c.has(e)&&(P=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=A[t],i=E[t];if(d.hasOwnProperty(t))continue;let n=!1;(V(e)&&V(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?k(t):g.protectedKeys[t]=!0:null!=e?k(t):c.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(d={...d,...A}),n&&t.blockInitialAnimation&&(T=!1);let C=!(w&&b)||P;T&&C&&h.push(...S.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let g=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),n=!1,g?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ip extends ih{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ix=t=>e=>iy(e)&&t(e,iv(e));function iw(t,e,i,n){return ig(t,e,ix(i),n)}function ib({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iT(t){return t.max-t.min}function iP(t,e,i,n=.5){t.origin=n,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iT(i)/iT(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iS(t,e,i,n){iP(t.x,e.x,i.x,n?n.originX:void 0),iP(t.y,e.y,i.y,n?n.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+iT(e)}function iE(t,e,i){t.min=e.min-i.min,t.max=t.min+iT(e)}function iM(t,e,i){iE(t.x,e.x,i.x),iE(t.y,e.y,i.y)}let ik=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:ik(),y:ik()}),iV=()=>({min:0,max:0}),iD=()=>({x:iV(),y:iV()});function iR(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iL({scale:t,scaleX:e,scaleY:i}){return!ij(t)||!ij(e)||!ij(i)}function iO(t){return iL(t)||iF(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iF(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iI(t,e=0,i=1,n,r){t.min=iB(t.min,e,i,n,r),t.max=iB(t.max,e,i,n,r)}function iU(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function iW(t,e,i,n,r=.5){let s=tE(t.min,t.max,r);iI(t,e,i,s,n)}function i$(t,e){iW(t.x,e.x,e.scaleX,e.scale,e.originX),iW(t.y,e.y,e.scaleY,e.scale,e.originY)}function i_(t,e){return ib(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iK(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iY=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iG(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iY(t.x,e.x)**2+iY(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=g;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iH(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iG("pointercancel"===t.type?this.lastMoveEventInfo:iH(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iy(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iH(iv(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=g;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iG(s,this.history)),this.removeListeners=F(iw(this.contextWindow,"pointermove",this.handlePointerMove),iw(this.contextWindow,"pointerup",this.handlePointerUp),iw(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iH(t,e){return e?{point:e(t.point)}:t}function iq(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iG({point:t},e){return{point:t,delta:iq(t,iZ(e)),offset:iq(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iZ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let s=U(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iR(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iT(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&m.postRender(()=>r(t,e)),R(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iR(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&m.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i3(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tE(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tE(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iK(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iR(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iK(e))return!1;let n=e.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i_(t,i),{scroll:r}=e;return r&&(iN(n.x,r.offset.x),iN(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ib(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iR(o=>{if(!i3(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e4(t,i,0,e,this.visualElement,!1))}stopAnimation(){iR(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iR(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iR(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tE(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iK(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iR(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iT(t),r=iT(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iR(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tE(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=iw(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iK(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let r=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iR(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i9 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i4=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i6 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i4(t),onStart:i4(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&m.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iw(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i8=i(5155);let{schedule:i7}=p(queueMicrotask,!1);var nt=i(2115),ne=i(2082),ni=i(869);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nh)na[t]=nh[t],z(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i8.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nh={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tP.parse(t);if(n.length>5)return t;let r=tP.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tE(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nc=i(6983);function nd(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let np=(t,e)=>t.depth-e.depth;class nm{constructor(){this.children=[],this.isDirty=!1}add(t){b(this.children,t),this.isDirty=!0}remove(t){T(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(np),this.isDirty=!1,this.children.forEach(t)}}function nf(t){return D(t)?t.get():t}let ng=["TopLeft","TopRight","BottomLeft","BottomRight"],ny=ng.length,nv=t=>"string"==typeof t?parseFloat(t):t,nx=t=>"number"==typeof t||tu.test(t);function nw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nb=nP(0,.5,t7),nT=nP(.5,.95,u);function nP(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nS(t,e){t.min=e.min,t.max=e.max}function nA(t,e){nS(t.x,e.x),nS(t.y,e.y)}function nE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nM(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nk(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(s.min,s.max,n);t===s&&(a-=e),t.min=nM(t.min,e,i,a,r),t.max=nM(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nC=["x","scaleX","originX"],nV=["y","scaleY","originY"];function nD(t,e,i,n){nk(t.x,e,nC,i?i.x:void 0,n?n.x:void 0),nk(t.y,e,nV,i?i.y:void 0,n?n.y:void 0)}function nR(t){return 0===t.translate&&1===t.scale}function nj(t){return nR(t.x)&&nR(t.y)}function nL(t,e){return t.min===e.min&&t.max===e.max}function nO(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nF(t,e){return nO(t.x,e.x)&&nO(t.y,e.y)}function nB(t){return iT(t.x)/iT(t.y)}function nI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nU{constructor(){this.members=[]}add(t){b(this.members,t),t.scheduleRender()}remove(t){if(T(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nW=["","X","Y","Z"],n$={visibility:"hidden"},n_=0;function nz(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nK({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=n_++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,d.value&&(nN.nodes=nN.calculatedTargetDeltas=nN.calculatedProjections=0),this.nodes.forEach(nH),this.nodes.forEach(n1),this.nodes.forEach(n2),this.nodes.forEach(nq),d.addProjectionMetrics&&d.addProjectionMetrics(nN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new P),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=nd(e)&&!(nd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(f(n),t(s-e))};return m.setup(n,!0),()=>f(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nF(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[L];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",m,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nZ);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nY),this.nodes.forEach(nX),this.clearAllSnapshots();let t=A.now();g.delta=B(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nG),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iT(this.snapshot.measuredBox.x)||iT(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nj(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iO(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iD();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iD();if(nA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nA(e,t),iN(e.x,r.offset.x),iN(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iD();nA(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i$(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iO(n.latestValues)&&i$(i,n.latestValues)}return iO(this.latestValues)&&i$(i,this.latestValues),i}removeTransform(t){let e=iD();nA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iO(i.latestValues))continue;iL(i.latestValues)&&i.updateSnapshot();let n=iD();nA(n,i.measurePageBox()),nD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iO(this.latestValues)&&nD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iA(s.x,o.x,a.x),iA(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nA(this.target,this.layout.layoutBox),iU(this.target,this.targetDelta)):nA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.target,t.target),nA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}d.value&&nN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iL(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nA(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&i$(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iU(t,s)),n&&iO(r.latestValues)&&i$(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iD());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nE(this.prevProjectionDelta.x,this.projectionDelta.x),nE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nI(this.projectionDelta.x,this.prevProjectionDelta.x)&&nI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),d.value&&nN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iD(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n6));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n9(o.x,t.x,n),n9(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m,f,g;iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=n,n4(p.x,m.x,f.x,g),n4(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,d=i,nL(u.x,d.x)&&nL(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=iD()),nA(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tE(0,i.opacity??1,nb(n)),t.opacityExit=tE(e.opacity??1,0,nT(n))):s&&(t.opacity=tE(e.opacity??1,i.opacity??1,n));for(let r=0;r<ny;r++){let s=`border${ng[r]}Radius`,o=nw(e,s),a=nw(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nx(o)===nx(a)?(t[s]=Math.max(tE(nv(o),nv(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{nr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=C(0)),this.currentAnimation=function(t,e,i){let n=D(t)?t:C(t);return n.start(e4("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iD();let e=iT(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iT(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nA(e,i),i$(e,r),iS(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nz("z",t,n,this.animationValues);for(let e=0;e<nW.length;e++)nz(`rotate${nW[e]}`,t,n,this.animationValues),nz(`skew${nW[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return n$;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nf(t?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=na[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nZ),this.root.sharedNodes.clear()}}}function nY(t){t.updateLayout()}function nX(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iR(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iT(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&iR(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iT(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iC();iS(o,i,e.layoutBox);let a=iC();s?iS(a,t.applyTransform(n,!0),e.measuredBox):iS(a,i,e.layoutBox);let l=!nj(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iD();iM(o,e.layoutBox,r.layoutBox);let a=iD();iM(a,i,s.layoutBox),nF(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nH(t){d.value&&nN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nq(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nG(t){t.clearSnapshot()}function nZ(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n2(t){t.calcProjection()}function n5(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n9(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n4(t,e,i,n){t.min=tE(e.min,i.min,n),t.max=tE(e.max,i.max,n)}function n6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n8={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nB(e)-nB(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nK({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nK({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var ra=i(2198);function rl(t,e){let i=(0,ra.K)(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function ru(t){return!("touch"===t.pointerType||im.x||im.y)}function rh(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&m.postRender(()=>r(e,iv(e)))}class rc extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rl(t,i),o=t=>{if(!ru(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{ru(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(rh(this.node,e,"Start"),t=>rh(this.node,t,"End"))))}unmount(){}}class rd extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rp=(t,e)=>!!e&&(t===e||rp(t,e.parentElement)),rm=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rg(t){return e=>{"Enter"===e.key&&t(e)}}function ry(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rv=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rg(()=>{if(rf.has(i))return;ry(i,"down");let t=rg(()=>{ry(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ry(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rx(t){return iy(t)&&!(im.x||im.y)}function rw(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&m.postRender(()=>r(e,iv(e)))}class rb extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=rl(t,i),o=t=>{let n=t.currentTarget;if(!rx(t))return;rf.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rf.has(n)&&rf.delete(n),rx(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rp(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eG.s)(t))&&(t.addEventListener("focus",t=>rv(t,r)),rm.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rw(this.node,e,"Start"),(t,{success:e})=>rw(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rT=new WeakMap,rP=new WeakMap,rS=t=>{let e=rT.get(t.target);e&&e(t)},rA=t=>{t.forEach(rS)},rE={some:0,all:1};class rM extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rE[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rP.has(i)||rP.set(i,{});let n=rP.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rA,{root:t,...e})),n[r]}(e);return rT.set(t,i),n.observe(t),()=>{rT.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rk=(0,nt.createContext)({strict:!1});var rC=i(1508);let rV=(0,nt.createContext)({});function rD(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rR(t){return!!(rD(t)||t.variants)}function rj(t){return Array.isArray(t)?t.join(" "):t}var rL=i(8972);let rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rF={};for(let t in rO)rF[t]={isEnabled:e=>rO[t].some(t=>!!e[t])};let rB=Symbol.for("motionComponentSymbol");var rI=i(845),rU=i(7494);function rN(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let rW=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r$={...H,transform:Math.round},r_={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:q,originX:td,originY:td,originZ:tu,zIndex:r$,fillOpacity:q,strokeOpacity:q,numOctaves:r$},rz={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rK=v.length;function rY(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(z(t)){r[t]=i;continue}{let e=rW(i,r_[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rK;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rW(a,r_[o]);if(!l){r=!1;let e=rz[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rX=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rH(t,e,i){for(let n in e)D(e[n])||rN(n,i)||(t[n]=e[n])}let rq={offset:"stroke-dashoffset",array:"stroke-dasharray"},rG={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rY(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rq:rG;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rQ=()=>({...rX(),attrs:{}}),rJ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),r0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r1(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||r0.has(t)}let r2=t=>!r1(t);try{!function(t){"function"==typeof t&&(r2=e=>e.startsWith("on")?!r1(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r3(t){if("string"!=typeof t||t.includes("-"));else if(r5.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r9=i(2885);let r4=t=>(e,i)=>{let n=(0,nt.useContext)(rV),s=(0,nt.useContext)(rI.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nf(a[t]);let{initial:l,animate:u}=t,h=rD(t),c=rR(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!r(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r9.M)(a)};function r6(t,e,i){let{style:n}=t,r={};for(let s in n)(D(n[s])||e.style&&D(e.style[s])||rN(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r8={useVisualState:r4({scrapeMotionValuesFromProps:r6,createRenderState:rX})};function r7(t,e,i){let n=r6(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(n[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let st={useVisualState:r4({scrapeMotionValuesFromProps:r7,createRenderState:rQ})},se=t=>e=>e.test(t),si=[H,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],sn=t=>si.find(se(t)),sr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ss=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,so=t=>/^0[^.\s]+$/u.test(t),sa=new Set(["brightness","contrast","saturate","opacity"]);function sl(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!sa.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let su=/\b([a-z-]*)\(.*?\)/gu,sh={...tP,getAnimatableNone:t=>{let e=t.match(su);return e?e.map(sl).join(" "):t}},sc={...r_,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:sh,WebkitFilter:sh},sd=t=>sc[t];function sp(t,e){let i=sd(t);return i!==sh&&(i=tP),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sm=new Set(["auto","none","0"]);class sf extends eO{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&Y(n=n.trim())){let r=function t(e,i,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=ss.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sr(t)?parseFloat(t):t}return Y(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!w.has(i)||2!==t.length)return;let[n,r]=t,s=sn(n),o=sn(r);if(s!==o)if(eA(s)&&eA(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ek[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||so(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sm.has(e)&&tx(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sp(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ek[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=ek[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sg=[...si,tm,tP],sy=t=>sg.find(se(t)),sv={current:null},sx={current:!1},sw=new WeakMap,sb=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sT{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eO,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rD(e),this.isVariantNode=rR(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sw.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sx.current||function(){if(sx.current=!0,rL.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sv.current=t.matches;t.addListener(e),e()}else sv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=x.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rF){let e=rF[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sb.length;e++){let i=sb[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(D(r))t.addValue(n,r);else if(D(s))t.addValue(n,C(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,C(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=C(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sr(i)||so(i))?i=parseFloat(i):!sy(i)&&tP.test(e)&&(i=sp(t,e)),this.setBaseTarget(t,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||D(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new P),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sP extends sT{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sS(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sA extends sP{constructor(){super(...arguments),this.type="html",this.renderInstance=sS}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?eb(e):eP(t,e);{let i=window.getComputedStyle(t),n=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i_(t,e)}build(t,e,i){rY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}}let sE=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sM extends sP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=sd(e);return t&&t.default||0}return e=sE.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r7(t,e,i)}build(t,e,i){rZ(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sS(t,e,void 0,n),e.attrs)t.setAttribute(sE.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=rJ(t.tagName),super.mount(t)}}let sk=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eH={animation:{Feature:ic},exit:{Feature:ip},inView:{Feature:rM},tap:{Feature:rb},focus:{Feature:rd},hover:{Feature:rc},pan:{Feature:i6},drag:{Feature:i9,ProjectionNode:ro,MeasureLayout:nu},layout:{ProjectionNode:ro,MeasureLayout:nu}},eq=(t,e)=>r3(t)?new sM(e):new sA(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rC.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(rD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rV));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rj(e),rj(i)])}(t),p=o(t,c);if(!c&&rL.B){n=0,l=0,(0,nt.useContext)(rk).strict;let t=function(t){let{drag:e,layout:i}=rF;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,d.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rV),o=(0,nt.useContext)(rk),a=(0,nt.useContext)(rI.t),l=(0,nt.useContext)(rC.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iK(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&d.current&&h.update(i,a)});let p=i[L],m=(0,nt.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,rU.E)(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,nt.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,p,h,r,t.ProjectionNode)}return(0,i8.jsxs)(rV.Provider,{value:d,children:[u&&d.visualElement?(0,i8.jsx)(u,{visualElement:d.visualElement,...h}):null,s(a,t,(i=d.visualElement,(0,nt.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iK(e)&&(e.current=t))},[i])),p,c,d.visualElement)]})}n&&function(t){for(let e in t)rF[e]={...rF[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rB]=a,u}({...r3(t)?st:r8,preloadedFeatures:eH,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r3(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rQ();return rZ(i,e,rJ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rH(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rH(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rX();return rY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r2(r)||!0===i&&r1(r)||!e&&!r1(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eq,Component:t})}))},4500:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))})},4633:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z"}))})},5695:(t,e,i)=>{var n=i(8999);i.o(n,"usePathname")&&i.d(e,{usePathname:function(){return n.usePathname}}),i.o(n,"useRouter")&&i.d(e,{useRouter:function(){return n.useRouter}})},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=i(2115);function r(t,e){let i=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let t=i.current;t&&(i.current=null,t());let e=r.current;e&&(r.current=null,e())}else t&&(i.current=s(t,n)),e&&(r.current=s(e,n))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return g},useLinkStatus:function(){return v}});let n=i(6966),r=i(5155),s=n._(i(2115)),o=i(2757),a=i(5227),l=i(9818),u=i(6654),h=i(9991),c=i(5929);i(3230);let d=i(4930),p=i(2664),m=i(6634);function f(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function g(t){let e,i,n,[o,g]=(0,s.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:x,as:w,children:b,prefetch:T=null,passHref:P,replace:S,shallow:A,scroll:E,onClick:M,onMouseEnter:k,onTouchStart:C,legacyBehavior:V=!1,onNavigate:D,ref:R,unstable_dynamicOnHover:j,...L}=t;e=b,V&&("string"==typeof e||"number"==typeof e)&&(e=(0,r.jsx)("a",{children:e}));let O=s.default.useContext(a.AppRouterContext),F=!1!==T,B=null===T?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:I,as:U}=s.default.useMemo(()=>{let t=f(x);return{href:t,as:w?f(w):t}},[x,w]);V&&(i=s.default.Children.only(e));let N=V?i&&"object"==typeof i&&i.ref:R,W=s.default.useCallback(t=>(null!==O&&(v.current=(0,d.mountLinkInstance)(t,I,O,B,F,g)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(t)}),[F,I,O,B,g]),$={ref:(0,u.useMergedRef)(W,N),onClick(t){V||"function"!=typeof M||M(t),V&&i.props&&"function"==typeof i.props.onClick&&i.props.onClick(t),O&&(t.defaultPrevented||function(t,e,i,n,r,o,a){let{nodeName:l}=t.currentTarget;if(!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){r&&(t.preventDefault(),location.replace(e));return}t.preventDefault(),s.default.startTransition(()=>{if(a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}(0,m.dispatchNavigateAction)(i||e,r?"replace":"push",null==o||o,n.current)})}}(t,I,U,v,S,E,D))},onMouseEnter(t){V||"function"!=typeof k||k(t),V&&i.props&&"function"==typeof i.props.onMouseEnter&&i.props.onMouseEnter(t),O&&F&&(0,d.onNavigationIntent)(t.currentTarget,!0===j)},onTouchStart:function(t){V||"function"!=typeof C||C(t),V&&i.props&&"function"==typeof i.props.onTouchStart&&i.props.onTouchStart(t),O&&F&&(0,d.onNavigationIntent)(t.currentTarget,!0===j)}};return(0,h.isAbsoluteUrl)(U)?$.href=U:V&&!P&&("a"!==i.type||"href"in i.props)||($.href=(0,c.addBasePath)(U)),n=V?s.default.cloneElement(i,$):(0,r.jsx)("a",{...L,...$,children:e}),(0,r.jsx)(y.Provider,{value:o,children:n})}i(3180);let y=(0,s.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(y);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7208:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}),n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"}))})},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},7652:(t,e,i)=>{i.d(e,{c3:()=>s});var n=i(3385);function r(t,e){return(...t)=>{try{return e(...t)}catch{throw Error(void 0)}}}let s=r(0,n.c3);r(0,n.kc)},8593:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},8791:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802"}))})},8859:(t,e)=>{function i(t){let e={};for(let[i,n]of t.entries()){let t=e[i];void 0===t?e[i]=n:Array.isArray(t)?t.push(n):e[i]=[t,n]}return e}function n(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function r(t){let e=new URLSearchParams;for(let[i,r]of Object.entries(t))if(Array.isArray(r))for(let t of r)e.append(i,n(t));else e.set(i,n(r));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,n]of e.entries())t.append(i,n)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return r}})},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9598:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(2115);let r=n.forwardRef(function(t,e){let{title:i,titleId:r,...s}=t;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:e,"aria-labelledby":r},s),i?n.createElement("title",{id:r},i):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))})},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function n(t){let e,i=!1;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];return i||(i=!0,e=t(...r)),e}}let r=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>r.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let n=await t.getInitialProps(e);if(i&&u(i))return n;if(!n)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);