'use client';

import { useState, useEffect } from 'react';
import { errorLogger, ErrorLog } from '../utils/errorLogger';
import ClientOnly from './ClientOnly';

export default function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [logs, setLogs] = useState<ErrorLog[]>([]);
  const [filter, setFilter] = useState<ErrorLog['type'] | 'all'>('all');

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const updateLogs = () => {
      setLogs(errorLogger.getLogs());
    };

    // Update logs every 2 seconds
    const interval = setInterval(updateLogs, 2000);
    updateLogs(); // Initial load

    return () => clearInterval(interval);
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const filteredLogs = filter === 'all' ? logs : logs.filter(log => log.type === filter);

  const getTypeColor = (type: ErrorLog['type']) => {
    switch (type) {
      case 'hydration': return 'text-yellow-400';
      case 'network': return 'text-red-400';
      case 'extension': return 'text-gray-400';
      case 'runtime': return 'text-red-500';
      default: return 'text-gray-300';
    }
  };

  return (
    <ClientOnly>
      <div className="fixed bottom-4 right-4 z-50">
        {/* Toggle Button */}
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-gray-800 hover:bg-gray-700 text-white p-3 rounded-full shadow-lg border border-gray-600 transition-colors"
          title="Debug Panel"
        >
          🐛 {logs.length > 0 && <span className="ml-1 bg-red-500 text-xs px-1 rounded-full">{logs.length}</span>}
        </button>

        {/* Debug Panel */}
        {isOpen && (
          <div className="absolute bottom-16 right-0 w-96 max-h-96 bg-gray-900 border border-gray-600 rounded-lg shadow-xl overflow-hidden">
            {/* Header */}
            <div className="bg-gray-800 p-3 border-b border-gray-600">
              <div className="flex items-center justify-between">
                <h3 className="text-white font-semibold">Debug Panel</h3>
                <div className="flex items-center space-x-2">
                  <select
                    value={filter}
                    onChange={(e) => setFilter(e.target.value as ErrorLog['type'] | 'all')}
                    className="bg-gray-700 text-white text-xs px-2 py-1 rounded border border-gray-600"
                  >
                    <option value="all">All ({logs.length})</option>
                    <option value="hydration">Hydration</option>
                    <option value="network">Network</option>
                    <option value="extension">Extension</option>
                    <option value="runtime">Runtime</option>
                  </select>
                  <button
                    type="button"
                    onClick={() => {
                      errorLogger.clearLogs();
                      setLogs([]);
                    }}
                    className="text-xs bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded transition-colors"
                  >
                    Clear
                  </button>
                </div>
              </div>
            </div>

            {/* Logs */}
            <div className="max-h-64 overflow-y-auto p-2">
              {filteredLogs.length === 0 ? (
                <div className="text-gray-400 text-sm text-center py-4">
                  No errors logged
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredLogs.slice(-10).reverse().map((log, index) => (
                    <div key={index} className="bg-gray-800 p-2 rounded text-xs">
                      <div className="flex items-center justify-between mb-1">
                        <span className={`font-semibold ${getTypeColor(log.type)}`}>
                          {log.type.toUpperCase()}
                        </span>
                        <span className="text-gray-400">
                          {new Date(log.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="text-gray-300 break-words">
                        {log.message}
                      </div>
                      {log.url && (
                        <div className="text-gray-500 mt-1 truncate">
                          {log.url}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="bg-gray-800 p-2 border-t border-gray-600">
              <button
                type="button"
                onClick={() => {
                  const data = errorLogger.exportLogs();
                  const blob = new Blob([data], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `debug-logs-${new Date().toISOString().split('T')[0]}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
                className="w-full text-xs bg-blue-600 hover:bg-blue-700 text-white py-1 rounded transition-colors"
              >
                Export Logs
              </button>
            </div>
          </div>
        )}
      </div>
    </ClientOnly>
  );
}
