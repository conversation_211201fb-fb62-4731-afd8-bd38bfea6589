exports.id=535,exports.ids=[535],exports.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(1042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},994:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\production\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\node_modules\\next-intl\\dist\\esm\\production\\shared\\NextIntlClientProvider.js","default")},1042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,o.isBailoutToCSRError)(t)||(0,u.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(8388),i=r(2637),o=r(1846),a=r(1162),s=r(4971),u=r(8479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=a.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return d},normalizeMetadataRoute:function(){return p}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),o=r(6341),a=r(4396),s=r(660),u=r(4722),c=r(2958),l=r(5499);function h(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,l.isGroupSegment)(e)||(0,l.isParallelRouteSegment)(e))&&(r=(0,s.djb2Hash)(t).toString(36).slice(0,6)),r}function f(e,t,r){let n=(0,u.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),l=(0,o.interpolateDynamicPath)(n,t,s),{name:f,ext:p}=i.default.parse(r),d=h(i.default.posix.join(e,f)),m=d?`-${d}`:"";return(0,c.normalizePathSep)(i.default.join(l,`${f}${m}${p}`))}function p(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=h(e),!t.endsWith("/route")){let{dir:e,name:n,ext:o}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${o}`,"route")}return t}function d(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return i}});let n=r(3763);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==a)return n.ReflectAdapter.get(t,a,i)},set(t,r,i,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,i,o);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,s??r,i,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},2765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),o=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:a,searchParams:s,search:u,hash:c,href:l,origin:h}=new URL(e,o);if(h!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(s):void 0,search:u,hash:c,href:l.slice(h.length)}}},3930:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1120),i=r(9769);let o=(0,n.cache)(async function(e){var t=await (0,i.A)(e);if(!t.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return t.messages});async function a(e){return o(e?.locale)}},4069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return s},RequestCookiesAdapter:function(){return u},appendMutableCookies:function(){return h},areCookiesMutableInCurrentPhase:function(){return d},getModifiedCookieValues:function(){return l},responseCookiesToRequestCookies:function(){return E},wrapWithMutableAccessCheck:function(){return p}});let n=r(3158),i=r(3763),o=r(9294),a=r(3033);class s extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new s}}class u{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return s.callable;default:return i.ReflectAdapter.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");function l(e){let t=e[c];return t&&Array.isArray(t)&&0!==t.length?t:[]}function h(e,t){let r=l(t);if(0===r.length)return!1;let i=new n.ResponseCookies(e),o=i.getAll();for(let e of r)i.set(e);for(let e of o)i.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],s=new Set,u=()=>{let e=o.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},l=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),l}finally{u()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),l}finally{u()}};default:return i.ReflectAdapter.get(e,t,r)}}});return l}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return i.ReflectAdapter.get(e,r,n)}}});return t}function d(e){return"action"===e.phase}function m(e){if(!d((0,a.getExpectedRequestStore)(e)))throw new s}function E(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return d},getRouteRegex:function(){return h},parseParameter:function(){return u}});let n=r(6143),i=r(1437),o=r(3293),a=r(2887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function u(e){let t=e.match(s);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e,t,r){let n={},u=1,l=[];for(let h of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>h.startsWith(e)),a=h.match(s);if(e&&a&&a[2]){let{key:t,optional:r,repeat:i}=c(a[2]);n[t]={pos:u++,repeat:i,optional:r},l.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:i}=c(a[2]);n[e]={pos:u++,repeat:t,optional:i},r&&a[1]&&l.push("/"+(0,o.escapeStringRegexp)(a[1]));let s=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(s=s.substring(1)),l.push(s)}else l.push("/"+(0,o.escapeStringRegexp)(h));t&&a&&a[3]&&l.push((0,o.escapeStringRegexp)(a[3]))}return{parameterizedRoute:l.join(""),groups:n}}function h(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:a}=l(e,r,n),s=o;return i||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:u}=e,{key:l,optional:h,repeat:f}=c(i),p=l.replace(/\W/g,"");s&&(p=""+s+p);let d=!1;(0===p.length||p.length>30)&&(d=!0),isNaN(parseInt(p.slice(0,1)))||(d=!0),d&&(p=n());let m=p in a;s?a[p]=""+s+l:a[p]=l;let E=r?(0,o.escapeStringRegexp)(r):"";return t=m&&u?"\\k<"+p+">":f?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",h?"(?:/"+E+t+")?":"/"+E+t}function p(e,t,r,u,c){let l,h=(l=0,()=>{let e="",t=++l;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},d=[];for(let l of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>l.startsWith(e)),a=l.match(s);if(e&&a&&a[2])d.push(f({getSafeRouteKey:h,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(a&&a[2]){u&&a[1]&&d.push("/"+(0,o.escapeStringRegexp)(a[1]));let e=f({getSafeRouteKey:h,segment:a[2],routeKeys:p,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});u&&a[1]&&(e=e.substring(1)),d.push(e)}else d.push("/"+(0,o.escapeStringRegexp)(l));r&&a&&a[3]&&d.push((0,o.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:d.join(""),routeKeys:p}}function d(e,t){var r,n,i;let o=p(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),a=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...h(e,t),namedRegex:"^"+a+"$",routeKeys:o.routeKeys}}function m(e,t){let{parameterizedRoute:r}=l(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4604:(e,t,r)=>{"use strict";r.d(t,{b:()=>eS,d:()=>eT,e:()=>eH,f:()=>ev,g:()=>e_,i:()=>eN,r:()=>eO});var n,i,o,a,s,u,c,l=function(e,t){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}l(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function p(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function d(e,t){var r=t&&t.cache?t.cache:y,n=t&&t.serializer?t.serializer:g;return(t&&t.strategy?t.strategy:function(e,t){var r,n,i=1===e.length?m:E;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function m(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),o=t.get(i);return void 0===o&&(o=e.call(this,n),t.set(i,o)),o}function E(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return void 0===o&&(o=e.apply(this,n),t.set(i,o)),o}Object.create,"function"==typeof SuppressedError&&SuppressedError;var g=function(){return JSON.stringify(arguments)},b=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),y={create:function(){return new b}},v={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,E.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,m.bind(this,e,r,n)}};function _(e){return e.type===i.literal}function T(e){return e.type===i.number}function A(e){return e.type===i.date}function R(e){return e.type===i.time}function S(e){return e.type===i.select}function P(e){return e.type===i.plural}function H(e){return e.type===i.tag}function O(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function N(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var B=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,I=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,M=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,L=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,C=/^(@+)?(\+|#+)?[rs]?$/g,w=/(\*)(0+)|(#+)(0+)|(0+)/g,D=/^(0+)$/;function U(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(C,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function G(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function F(e){var t=G(e);return t||{}}var x={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},k=new RegExp("^".concat(B.source,"*")),j=new RegExp("".concat(B.source,"*$"));function V(e,t){return{start:e,end:t}}var X=!!String.prototype.startsWith&&"_a".startsWith("a",1),$=!!String.fromCodePoint,K=!!Object.fromEntries,q=!!String.prototype.codePointAt,Z=!!String.prototype.trimStart,W=!!String.prototype.trimEnd,Y=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},z=!0;try{z=(null==(a=ei("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){z=!1}var Q=X?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},J=$?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},ee=K?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},et=q?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},er=Z?function(e){return e.trimStart()}:function(e){return e.replace(k,"")},en=W?function(e){return e.trimEnd()}:function(e){return e.replace(j,"")};function ei(e,t){return new RegExp(e,t)}if(z){var eo=ei("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return eo.lastIndex=t,null!=(r=eo.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,i=et(e,t);if(void 0===i||eu(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return J.apply(void 0,r)};var ea=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;o.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var u=this.clonePosition();this.bump(),o.push({type:i.pound,location:V(u,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&es(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;o.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;o.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,V(this.clonePosition(),this.clonePosition()));else break}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:V(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,V(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,u=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,V(r,this.clonePosition()));if(this.isEOF()||!es(this.char()))return this.error(n.INVALID_TAG,V(u,this.clonePosition()));var c=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,V(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:s,location:V(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,V(u,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=V(r,this.clonePosition());return{val:{type:i.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(es(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return J.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),J(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,V(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,V(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:V(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,V(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:V(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,u=this.clonePosition(),c=this.parseIdentifierIfPossible().value,l=this.clonePosition();switch(c){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,V(u,l));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var p=this.clonePosition(),d=this.parseSimpleArgStyleIfPossible();if(d.err)return d;var m=en(d.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,V(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:V(p,this.clonePosition())}}var E=this.tryParseArgumentClose(a);if(E.err)return E;var g=V(a,this.clonePosition());if(h&&Q(null==h?void 0:h.style,"::",0)){var b=er(h.style.slice(2));if("number"===c){var d=this.parseNumberSkeletonFromString(b,h.styleLocation);if(d.err)return d;return{val:{type:i.number,value:r,location:g,style:d.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var y,v=b;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var a=1+(1&o),s=o<2?1:3+(o>>1),u=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(x[t||""]||x[n||""]||x["".concat(n,"-001")]||x["001"])[0]}(t);for(("H"==u||"k"==u)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=u+r}else"J"===i?r+="H":r+=i}return r}(b,this.locale));var m={type:o.dateTime,pattern:v,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},v.replace(I,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===c?i.date:i.time,value:r,location:g,style:m},err:null}}return{val:{type:"number"===c?i.number:"date"===c?i.date:i.time,value:r,location:g,style:null!=(s=null==h?void 0:h.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,V(_,f({},_)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==c&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,V(this.clonePosition(),this.clonePosition()));this.bumpSpace();var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(d.err)return d;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=d.val}var R=this.tryParsePluralOrSelectOptions(e,c,t,T);if(R.err)return R;var E=this.tryParseArgumentClose(a);if(E.err)return E;var S=V(a,this.clonePosition());if("select"===c)return{val:{type:i.select,value:r,options:ee(R.val),location:S},err:null};return{val:{type:i.plural,value:r,options:ee(R.val),offset:A,pluralType:"plural"===c?"cardinal":"ordinal",location:S},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,V(u,l))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,V(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(M).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],a=i.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:o,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),F(t))},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),F(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(w,function(e,r,n,i,o,a){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(D.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(L.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(L,function(e,r,n,i,o,a){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&a?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=f(f({},t),U(i)));continue}if(C.test(n.stem)){t=f(f({},t),U(n.stem));continue}var o=G(n.stem);o&&(t=f(f({},t),o));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!D.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,a=!1,s=[],u=new Set,c=i.value,l=i.location;;){if(0===c.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;l=V(h,this.clonePosition()),c=this.message.slice(h.offset,this.offset())}else break}if(u.has(c))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);"other"===c&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,V(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(e+1,t,r);if(d.err)return d;var m=this.tryParseArgumentClose(p);if(m.err)return m;s.push([c,{value:d.val,location:V(p,this.clonePosition())}]),u.add(c),this.bumpSpace(),c=(o=this.parseIdentifierIfPossible()).value,l=o.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,V(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,V(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)i=!0,o=10*o+(a-48),this.bump();else break}var s=V(n,this.clonePosition());return i?Y(o*=r)?{val:o,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=et(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Q(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&eu(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function es(e){return e>=97&&e<=122||e>=65&&e<=90}function eu(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ec(e,t){void 0===t&&(t={});var r=new ea(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,S(t)||P(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else T(t)&&O(t.style)||(A(t)||R(t))&&N(t.style)?delete t.style.location:H(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(u||(u={}));var el=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),eh=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),u.INVALID_VALUE,i)||this}return h(t,e),t}(el),ef=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),u.INVALID_VALUE,n)||this}return h(t,e),t}(el),ep=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),u.MISSING_VALUE,r)||this}return h(t,e),t}(el);function ed(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(c||(c={}));var em=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var a,s,l=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=l.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===c.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,o,a,s,l){if(1===t.length&&_(t[0]))return[{type:c.literal,value:t[0].value}];for(var h=[],f=0;f<t.length;f++){var p=t[f];if(_(p)){h.push({type:c.literal,value:p.value});continue}if(p.type===i.pound){"number"==typeof s&&h.push({type:c.literal,value:n.getNumberFormat(r).format(s)});continue}var d=p.value;if(!(a&&d in a))throw new ep(d,l);var m=a[d];if(p.type===i.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),h.push({type:"string"==typeof m?c.literal:c.object,value:m});continue}if(A(p)){var E="string"==typeof p.style?o.date[p.style]:N(p.style)?p.style.parsedOptions:void 0;h.push({type:c.literal,value:n.getDateTimeFormat(r,E).format(m)});continue}if(R(p)){var E="string"==typeof p.style?o.time[p.style]:N(p.style)?p.style.parsedOptions:o.time.medium;h.push({type:c.literal,value:n.getDateTimeFormat(r,E).format(m)});continue}if(T(p)){var E="string"==typeof p.style?o.number[p.style]:O(p.style)?p.style.parsedOptions:void 0;E&&E.scale&&(m*=E.scale||1),h.push({type:c.literal,value:n.getNumberFormat(r,E).format(m)});continue}if(H(p)){var g=p.children,b=p.value,y=a[b];if("function"!=typeof y)throw new ef(b,"function",l);var v=y(e(g,r,n,o,a,s).map(function(e){return e.value}));Array.isArray(v)||(v=[v]),h.push.apply(h,v.map(function(e){return{type:"string"==typeof e?c.literal:c.object,value:e}}))}if(S(p)){var B=p.options[m]||p.options.other;if(!B)throw new eh(p.value,m,Object.keys(p.options),l);h.push.apply(h,e(B.value,r,n,o,a));continue}if(P(p)){var B=p.options["=".concat(m)];if(!B){if(!Intl.PluralRules)throw new el('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',u.MISSING_INTL_API,l);var I=n.getPluralRules(r,{type:p.pluralType}).select(m-(p.offset||0));B=p.options[I]||p.options.other}if(!B)throw new eh(p.value,m,Object.keys(p.options),l);h.push.apply(h,e(B.value,r,n,o,a,m-(p.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===c.literal&&t.type===c.literal?r.value+=t.value:e.push(t),e},[])}(l.ast,l.locales,l.formatters,l.formats,e,void 0,l.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=l.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(l.locales)[0]}},this.getAst=function(){return l.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=o||{},m=(h.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(h,["formatters"]));this.ast=e.__parse(t,f(f({},m),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,i;return e[t]=(r=a[t],(i=n[t])?f(f(f({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),i[t]||{}),e},{})):r),e},f({},a)):a),this.formatters=o&&o.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,p([void 0],t,!1)))},{cache:ed(s.number),strategy:v.variadic}),getDateTimeFormat:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,p([void 0],t,!1)))},{cache:ed(s.dateTime),strategy:v.variadic}),getPluralRules:d(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,p([void 0],t,!1)))},{cache:ed(s.pluralRules),strategy:v.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ec,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),eE=r(1120);class eg extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var eb=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(eb||{});function ey(...e){return e.filter(Boolean).join(".")}function ev(e){return ey(e.namespace,e.key)}function e_(e){console.error(e)}function eT(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eA(e,t){return d(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:v.variadic})}function eR(e,t){return eA((...t)=>new e(...t),t)}function eS(e){return{getDateTimeFormat:eR(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eR(Intl.NumberFormat,e.number),getPluralRules:eR(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eR(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eR(Intl.ListFormat,e.list),getDisplayNames:eR(Intl.DisplayNames,e.displayNames)}}function eP(e,t,r,n){let i=ey(n,r);if(!t)throw Error(i);let o=t;return r.split(".").forEach(t=>{let r=o[t];if(null==t||null==r)throw Error(i+` (${e})`);o=r}),o}function eH(e){let t=function(e,t,r,n=e_){try{if(!t)throw Error(void 0);let n=r?eP(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new eg(eb.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=ev,locale:i,messagesOrError:o,namespace:a,onError:s,timeZone:u}){let c=o instanceof eg;function l(e,t,r){let i=new eg(t,r);return s(i),n({error:i,key:e,namespace:a})}function h(s,h,f){var p;let d,m;if(c)return n({error:o,key:s,namespace:a});try{d=eP(i,o,s,a)}catch(e){return l(s,eb.MISSING_MESSAGE,e.message)}if("object"==typeof d){let e;return l(s,Array.isArray(d)?eb.INVALID_MESSAGE:eb.INSUFFICIENT_PATH,e)}let E=(p=d,h?void 0:p);if(E)return E;r.getMessageFormat||(r.getMessageFormat=eA((...e)=>new em(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{m=r.getMessageFormat(d,i,function(e,t,r){let n=em.formats.date,i=em.formats.time,o={...e?.dateTime,...t?.dateTime},a={date:{...n,...o},time:{...i,...o},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,f,u),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:u,...t})}})}catch(e){return l(s,eb.INVALID_MESSAGE,e.message)}try{let e=m.format(h?function(e){let t={};return Object.keys(e).forEach(r=>{let n,i=0,o=e[r];n="function"==typeof o?e=>{let t=o(e);return(0,eE.isValidElement)(t)?(0,eE.cloneElement)(t,{key:r+i++}):t}:o,t[r]=n}),t}(h):h);if(null==e)throw Error(void 0);return(0,eE.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return l(s,eb.FORMATTING_ERROR,e.message)}}function f(e,t,r){let n=h(e,t,r);return"string"!=typeof n?l(e,eb.INVALID_MESSAGE,void 0):n}return f.rich=h,f.markup=(e,t,r)=>h(e,t,r),f.raw=e=>{if(c)return n({error:o,key:e,namespace:a});try{return eP(i,o,e,a)}catch(t){return l(e,eb.MISSING_MESSAGE,t.message)}},f.has=e=>{if(c)return!1;try{return eP(i,o,e,a),!0}catch{return!1}},f}({...e,messagesOrError:t})}function eO(e,t){return e===t?void 0:e.slice((t+".").length)}function eN({formats:e,getMessageFallback:t,messages:r,onError:n,...i}){return{...i,formats:e||void 0,messages:r||void 0,onError:n||e_,getMessageFallback:t||ev}}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(5531),i=r(5499);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return d},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return E},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return c},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function l(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class d extends Error{}class m extends Error{}class E extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},5196:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(8521),i=r(687);function o({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(n.Dk,{locale:e,...t})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var s=1,u="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){u+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);u+=e[o++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!u)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:u}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,a="[^"+i(t.delimiter||"/#?")+"]+?",s=[],u=0,c=0,l="",h=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},f=function(e){var t=h(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=h("CHAR")||h("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var d=h("CHAR"),m=h("NAME"),E=h("PATTERN");if(m||E){var g=d||"";-1===o.indexOf(g)&&(l+=g,g=""),l&&(s.push(l),l=""),s.push({name:m||u++,prefix:g,suffix:"",pattern:E||a,modifier:h("MODIFIER")||""});continue}var b=d||h("ESCAPED_CHAR");if(b){l+=b;continue}if(l&&(s.push(l),l=""),h("OPEN")){var g=p(),y=h("NAME")||"",v=h("PATTERN")||"",_=p();f("CLOSE"),s.push({name:y||(v?u++:""),pattern:y&&!v?a:v,prefix:g,suffix:_,modifier:h("MODIFIER")||""});continue}f("END")}return s}function r(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,s=void 0===a||a,u=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var a=t?t[o.name]:void 0,c="?"===o.modifier||"*"===o.modifier,l="*"===o.modifier||"+"===o.modifier;if(Array.isArray(a)){if(!l)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===a.length){if(c)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var h=0;h<a.length;h++){var f=i(a[h],o);if(s&&!u[n].test(f))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix}continue}if("string"==typeof a||"number"==typeof a){var f=i(String(a),o);if(s&&!u[n].test(f))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix;continue}if(!c){var p=l?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+p)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],a=n.index,s=Object.create(null),u=1;u<n.length;u++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(u);return{path:o,index:a,params:s}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,s=r.start,u=r.end,c=r.encode,l=void 0===c?function(e){return e}:c,h="["+i(r.endsWith||"")+"]|$",f="["+i(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",d=0;d<e.length;d++){var m=e[d];if("string"==typeof m)p+=i(l(m));else{var E=i(l(m.prefix)),g=i(l(m.suffix));if(m.pattern)if(t&&t.push(m),E||g)if("+"===m.modifier||"*"===m.modifier){var b="*"===m.modifier?"?":"";p+="(?:"+E+"((?:"+m.pattern+")(?:"+g+E+"(?:"+m.pattern+"))*)"+g+")"+b}else p+="(?:"+E+"("+m.pattern+")"+g+")"+m.modifier;else p+="("+m.pattern+")"+m.modifier;else p+="(?:"+E+g+")"+m.modifier}}if(void 0===u||u)a||(p+=f+"?"),p+=r.endsWith?"(?="+h+")":"$";else{var y=e[e.length-1],v="string"==typeof y?f.indexOf(y[y.length-1])>-1:void 0===y;a||(p+="(?:"+f+"(?="+h+"))?"),v||(p+="(?="+f+"|"+h+")")}return new RegExp(p,o(r))}function s(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var u=0;u<i.length;u++)r.push({name:u,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return s(e,r,n).source}).join("|")+")",o(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(s(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=s})(),e.exports=t})()},5471:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{A:()=>n})},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return l},matchHas:function(){return c},parseDestination:function(){return h},prepareDestination:function(){return f}});let n=r(5362),i=r(3293),o=r(6759),a=r(1437),s=r(8212);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},o=r=>{let n,o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,s.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>o(e))||n.some(e=>o(e)))&&i}function l(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function h(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,o.parseUrl)(t),n=r.pathname;n&&(n=u(n));let a=r.href;a&&(a=u(a));let s=r.hostname;s&&(s=u(s));let c=r.hash;return c&&(c=u(c)),{...r,pathname:n,hostname:s,href:a,hash:c}}function f(e){let t,r,i=Object.assign({},e.query),o=h(e),{hostname:s,query:c}=o,f=o.pathname;o.hash&&(f=""+f+o.hash);let p=[],d=[];for(let e of((0,n.pathToRegexp)(f,d),d))p.push(e.name);if(s){let e=[];for(let t of((0,n.pathToRegexp)(s,e),e))p.push(t.name)}let m=(0,n.compile)(f,{validate:!1});for(let[r,i]of(s&&(t=(0,n.compile)(s,{validate:!1})),Object.entries(c)))Array.isArray(i)?c[r]=i.map(t=>l(u(t),e.params)):"string"==typeof i&&(c[r]=l(u(i),e.params));let E=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!E.some(e=>p.includes(e)))for(let t of E)t in c||(c[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=m(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(i?"#":"")+(i||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...i,...o.query},{newUrl:r,destQuery:c,parsedDestination:o}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return h}});let n=r(2584),i=r(9294),o=r(3033),a=r(4971),s=r(23),u=r(8388),c=r(6926),l=(r(4523),r(8719));function h(){let e=i.workAsyncStorage.getStore(),t=o.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,c=t;let n=f.get(c);if(n)return n;let i=(0,u.makeHangingPromise)(c.renderSignal,"`headers()`");return f.set(c,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${d(arguments[0])}, ...)\``,t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},delete:{value:function(){let e=`\`headers().delete(${d(arguments[0])})\``,t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},get:{value:function(){let e=`\`headers().get(${d(arguments[0])})\``,t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},has:{value:function(){let e=`\`headers().has(${d(arguments[0])})\``,t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},set:{value:function(){let e=`\`headers().set(${d(arguments[0])}, ...)\``,t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},keys:{value:function(){let e="`headers().keys()`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},values:{value:function(){let e="`headers().values()`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},entries:{value:function(){let e="`headers().entries()`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=E(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,c)}}}),i}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return p((0,o.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function d(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(E);function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6294:(e,t,r)=>{"use strict";let n=r(3033),i=r(9294),o=r(4971),a=r(6926),s=r(23),u=r(8479);function c(){let e=i.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return l(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return l(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function l(e,t){let r,n=h.get(c);return n||(r=f(e),h.set(e,r),r)}let h=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let d=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=i.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new s.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,o.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,o.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new u.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return E},interpolateDynamicPath:function(){return d},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return p}});let n=r(9551),i=r(1959),o=r(2437),a=r(4396),s=r(8034),u=r(5526),c=r(2887),l=r(4722),h=r(6143),f=r(7912);function p(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==h.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(h.NEXT_QUERY_PARAM_PREFIX),o=e!==h.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(h.NEXT_INTERCEPTION_MARKER_PREFIX);(n||o||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function d(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:o,repeat:a}=r.groups[n],s=`[${a?"...":""}${n}]`;o&&(s=`[${s}]`);let u=t[n];i=Array.isArray(u)?u.map(e=>e&&encodeURIComponent(e)).join("/"):u?encodeURIComponent(u):"",e=e.replaceAll(s,i)}return e}function m(e,t,r,n){let i={};for(let o of Object.keys(t.groups)){let a=e[o];"string"==typeof a?a=(0,l.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(l.normalizeRscURL));let s=r[o],u=t.groups[o].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(u&&n))return{params:{},hasValidParams:!1};u&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${o}]]`))&&(a=void 0,delete e[o]),a&&"string"==typeof a&&t.groups[o].repeat&&(a=a.split("/")),a&&(i[o]=a)}return{params:i,hasValidParams:!0}}function E({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:l,trailingSlash:h,caseSensitive:E}){let g,b,y;return l&&(g=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),y=(b=(0,s.getRouteMatcher)(g))(e)),{handleRewrites:function(a,s){let f={},p=s.pathname,d=n=>{let c=(0,o.getPathMatch)(n.source+(h?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!E});if(!s.pathname)return!1;let d=c(s.pathname);if((n.has||n.missing)&&d){let e=(0,u.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(d,e):d=!1}if(d){let{parsedDestination:o,destQuery:a}=(0,u.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:d,query:s.query});if(o.protocol)return!0;if(Object.assign(f,a,d),Object.assign(s.query,o.query),delete o.query,Object.assign(s,o),!(p=s.pathname))return!1;if(r&&(p=p.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(p,t.locales);p=e.pathname,s.query.nextInternalLocale=e.detectedLocale||d.nextInternalLocale}if(p===e)return!0;if(l&&b){let e=b(p);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])d(e);if(p!==e){let t=!1;for(let e of n.afterFiles||[])if(t=d(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(p||"");return t===(0,c.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of n.fallback||[])if(t=d(e))break}}return f},defaultRouteRegex:g,dynamicRouteMatcher:b,defaultRouteMatches:y,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:r}=g,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,f.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let o=r[e];if(!o)continue;let a=t[o],s=n[e];if(!a.optional&&!s)return null;i[a.pos]=s}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>g&&y?m(e,g,y,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,g),interpolateDynamicPath:(e,t)=>d(e,t,g)}}function g(e,t){return"string"==typeof e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[h.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var u=o[s],c=u.indexOf("=");if(!(c<0)){var l=u.substr(0,c).trim(),h=u.substr(++c,u.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==i[l]&&(i[l]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var u=e+"="+s;if(null!=o.maxAge){var c=o.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");u+="; Max-Age="+Math.floor(c)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");u+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");u+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");u+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(u+="; HttpOnly"),o.secure&&(u+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(2785),i=r(3736);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return h},getRedirectTypeFromError:function(){return l},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return u},redirect:function(){return s}});let n=r(2836),i=r(9026),o=r(9121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let o=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return o.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",o}function s(e,t){var r;throw null!=t||(t=(null==o||null==(r=o.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function u(e,t){throw void 0===t&&(t=i.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function l(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function h(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l},RedirectType:function(){return i.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return u.unstable_rethrow}});let n=r(6897),i=r(9026),o=r(2765),a=r(8976),s=r(899),u=r(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class l extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>o(e)):a[e]=o(r))}return a}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return u},isMetadataPage:function(){return h},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return l}});let n=r(2958),i=r(4722),o=r(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],u=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let i=(r?"":"?")+"$",o=`\\d?${r?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${u(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${u(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${u(["xml"],t)}${i}`),RegExp(`[\\\\/]${a.icon.filename}${o}${u(a.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${a.apple.filename}${o}${u(a.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${a.openGraph.filename}${o}${u(a.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${a.twitter.filename}${o}${u(a.twitter.extensions,t)}${i}`)],c=(0,n.normalizePathSep)(e);return s.some(e=>e.test(c))}function l(e){let t=e.replace(/\/route$/,"");return(0,o.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function h(e){return!(0,o.isAppRouteRoute)(e)&&c(e,[],!1)}function f(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,o.isAppRouteRoute)(e)&&c(t,[],!1)}},8521:(e,t,r)=>{"use strict";r.d(t,{Dk:()=>eB,kc:()=>eD,Ym:()=>ew,c3:()=>eC});var n,i,o,a,s,u,c,l=r(3210),h=function(e,t){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function f(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var p=function(){return(p=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.create;function d(e,t,r){if(r||2==arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function m(e,t){var r=t&&t.cache?t.cache:v,n=t&&t.serializer?t.serializer:b;return(t&&t.strategy?t.strategy:function(e,t){var r,n,i=1===e.length?E:g;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function E(e,t,r,n){var i=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),o=t.get(i);return void 0===o&&(o=e.call(this,n),t.set(i,o)),o}function g(e,t,r){var n=Array.prototype.slice.call(arguments,3),i=r(n),o=t.get(i);return void 0===o&&(o=e.apply(this,n),t.set(i,o)),o}Object.create,"function"==typeof SuppressedError&&SuppressedError;var b=function(){return JSON.stringify(arguments)},y=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),v={create:function(){return new y}},_={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,g.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,E.bind(this,e,r,n)}};function T(e){return e.type===i.literal}function A(e){return e.type===i.number}function R(e){return e.type===i.date}function S(e){return e.type===i.time}function P(e){return e.type===i.select}function H(e){return e.type===i.plural}function O(e){return e.type===i.tag}function N(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function B(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var I=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,M=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,L=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,C=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,w=/^(@+)?(\+|#+)?[rs]?$/g,D=/(\*)(0+)|(#+)(0+)|(0+)/g,U=/^(0+)$/;function G(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(w,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function F(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function x(e){var t=F(e);return t||{}}var k={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},j=new RegExp("^".concat(I.source,"*")),V=new RegExp("".concat(I.source,"*$"));function X(e,t){return{start:e,end:t}}var $=!!String.prototype.startsWith&&"_a".startsWith("a",1),K=!!String.fromCodePoint,q=!!Object.fromEntries,Z=!!String.prototype.codePointAt,W=!!String.prototype.trimStart,Y=!!String.prototype.trimEnd,z=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},Q=!0;try{Q=(null==(a=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){Q=!1}var J=$?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},ee=K?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},et=q?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},er=Z?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},en=W?function(e){return e.trimStart()}:function(e){return e.replace(j,"")},ei=Y?function(e){return e.trimEnd()}:function(e){return e.replace(V,"")};function eo(e,t){return new RegExp(e,t)}if(Q){var ea=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return ea.lastIndex=t,null!=(r=ea.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,i=er(e,t);if(void 0===i||ec(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return ee.apply(void 0,r)};var es=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;o.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var u=this.clonePosition();this.bump(),o.push({type:i.pound,location:X(u,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&eu(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;o.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;o.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,X(this.clonePosition(),this.clonePosition()));else break}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:X(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,X(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,u=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,X(r,this.clonePosition()));if(this.isEOF()||!eu(this.char()))return this.error(n.INVALID_TAG,X(u,this.clonePosition()));var c=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,X(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:s,location:X(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,X(u,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=X(r,this.clonePosition());return{val:{type:i.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(eu(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return ee.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),ee(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,X(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,X(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:X(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,X(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:X(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,u=this.clonePosition(),c=this.parseIdentifierIfPossible().value,l=this.clonePosition();switch(c){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,X(u,l));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),d=this.parseSimpleArgStyleIfPossible();if(d.err)return d;var m=ei(d.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,X(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:X(f,this.clonePosition())}}var E=this.tryParseArgumentClose(a);if(E.err)return E;var g=X(a,this.clonePosition());if(h&&J(null==h?void 0:h.style,"::",0)){var b=en(h.style.slice(2));if("number"===c){var d=this.parseNumberSkeletonFromString(b,h.styleLocation);if(d.err)return d;return{val:{type:i.number,value:r,location:g,style:d.val},err:null}}if(0===b.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var y,v=b;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var a=1+(1&o),s=o<2?1:3+(o>>1),u=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(k[t||""]||k[n||""]||k["".concat(n,"-001")]||k["001"])[0]}(t);for(("H"==u||"k"==u)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=u+r}else"J"===i?r+="H":r+=i}return r}(b,this.locale));var m={type:o.dateTime,pattern:v,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(y={},v.replace(M,function(e){var t=e.length;switch(e[0]){case"G":y.era=4===t?"long":5===t?"narrow":"short";break;case"y":y.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":y.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":y.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":y.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");y.weekday=["short","long","narrow","short"][t-4];break;case"a":y.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":y.hourCycle="h12",y.hour=["numeric","2-digit"][t-1];break;case"H":y.hourCycle="h23",y.hour=["numeric","2-digit"][t-1];break;case"K":y.hourCycle="h11",y.hour=["numeric","2-digit"][t-1];break;case"k":y.hourCycle="h24",y.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":y.minute=["numeric","2-digit"][t-1];break;case"s":y.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":y.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),y):{}};return{val:{type:"date"===c?i.date:i.time,value:r,location:g,style:m},err:null}}return{val:{type:"number"===c?i.number:"date"===c?i.date:i.time,value:r,location:g,style:null!=(s=null==h?void 0:h.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var _=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,X(_,p({},_)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==c&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,X(this.clonePosition(),this.clonePosition()));this.bumpSpace();var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(d.err)return d;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=d.val}var R=this.tryParsePluralOrSelectOptions(e,c,t,T);if(R.err)return R;var E=this.tryParseArgumentClose(a);if(E.err)return E;var S=X(a,this.clonePosition());if("select"===c)return{val:{type:i.select,value:r,options:et(R.val),location:S},err:null};return{val:{type:i.plural,value:r,options:et(R.val),offset:A,pluralType:"plural"===c?"cardinal":"ordinal",location:S},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,X(u,l))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,X(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,X(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(L).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],a=i.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:o,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=p(p(p({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return p(p({},e),x(t))},{}));continue;case"engineering":t=p(p(p({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return p(p({},e),x(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(D,function(e,r,n,i,o,a){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(U.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(C.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(C,function(e,r,n,i,o,a){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&a?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=p(p({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=p(p({},t),G(i)));continue}if(w.test(n.stem)){t=p(p({},t),G(n.stem));continue}var o=F(n.stem);o&&(t=p(p({},t),o));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!U.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=p(p({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,a=!1,s=[],u=new Set,c=i.value,l=i.location;;){if(0===c.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;l=X(h,this.clonePosition()),c=this.message.slice(h.offset,this.offset())}else break}if(u.has(c))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);"other"===c&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,X(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(e+1,t,r);if(d.err)return d;var m=this.tryParseArgumentClose(p);if(m.err)return m;s.push([c,{value:d.val,location:X(p,this.clonePosition())}]),u.add(c),this.bumpSpace(),c=(o=this.parseIdentifierIfPossible()).value,l=o.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,X(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,X(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)i=!0,o=10*o+(a-48),this.bump();else break}var s=X(n,this.clonePosition());return i?z(o*=r)?{val:o,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=er(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(J(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&ec(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function eu(e){return e>=97&&e<=122||e>=65&&e<=90}function ec(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function el(e,t){void 0===t&&(t={});var r=new es(e,t=p({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,P(t)||H(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else A(t)&&N(t.style)||(R(t)||S(t))&&B(t.style)?delete t.style.location:O(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(u||(u={}));var eh=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return f(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ef=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),u.INVALID_VALUE,i)||this}return f(t,e),t}(eh),ep=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),u.INVALID_VALUE,n)||this}return f(t,e),t}(eh),ed=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),u.MISSING_VALUE,r)||this}return f(t,e),t}(eh);function em(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(c||(c={}));var eE=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var a,s,l=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=l.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===c.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,o,a,s,l){if(1===t.length&&T(t[0]))return[{type:c.literal,value:t[0].value}];for(var h=[],f=0;f<t.length;f++){var p=t[f];if(T(p)){h.push({type:c.literal,value:p.value});continue}if(p.type===i.pound){"number"==typeof s&&h.push({type:c.literal,value:n.getNumberFormat(r).format(s)});continue}var d=p.value;if(!(a&&d in a))throw new ed(d,l);var m=a[d];if(p.type===i.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),h.push({type:"string"==typeof m?c.literal:c.object,value:m});continue}if(R(p)){var E="string"==typeof p.style?o.date[p.style]:B(p.style)?p.style.parsedOptions:void 0;h.push({type:c.literal,value:n.getDateTimeFormat(r,E).format(m)});continue}if(S(p)){var E="string"==typeof p.style?o.time[p.style]:B(p.style)?p.style.parsedOptions:o.time.medium;h.push({type:c.literal,value:n.getDateTimeFormat(r,E).format(m)});continue}if(A(p)){var E="string"==typeof p.style?o.number[p.style]:N(p.style)?p.style.parsedOptions:void 0;E&&E.scale&&(m*=E.scale||1),h.push({type:c.literal,value:n.getNumberFormat(r,E).format(m)});continue}if(O(p)){var g=p.children,b=p.value,y=a[b];if("function"!=typeof y)throw new ep(b,"function",l);var v=y(e(g,r,n,o,a,s).map(function(e){return e.value}));Array.isArray(v)||(v=[v]),h.push.apply(h,v.map(function(e){return{type:"string"==typeof e?c.literal:c.object,value:e}}))}if(P(p)){var _=p.options[m]||p.options.other;if(!_)throw new ef(p.value,m,Object.keys(p.options),l);h.push.apply(h,e(_.value,r,n,o,a));continue}if(H(p)){var _=p.options["=".concat(m)];if(!_){if(!Intl.PluralRules)throw new eh('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',u.MISSING_INTL_API,l);var I=n.getPluralRules(r,{type:p.pluralType}).select(m-(p.offset||0));_=p.options[I]||p.options.other}if(!_)throw new ef(p.value,m,Object.keys(p.options),l);h.push.apply(h,e(_.value,r,n,o,a,m-(p.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===c.literal&&t.type===c.literal?r.value+=t.value:e.push(t),e},[])}(l.ast,l.locales,l.formatters,l.formats,e,void 0,l.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=l.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(l.locales)[0]}},this.getAst=function(){return l.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=o||{},f=(h.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(h,["formatters"]));this.ast=e.__parse(t,p(p({},f),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,i;return e[t]=(r=a[t],(i=n[t])?p(p(p({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=p(p({},r[t]),i[t]||{}),e},{})):r),e},p({},a)):a),this.formatters=o&&o.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,d([void 0],t,!1)))},{cache:em(s.number),strategy:_.variadic}),getDateTimeFormat:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,d([void 0],t,!1)))},{cache:em(s.dateTime),strategy:_.variadic}),getPluralRules:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,d([void 0],t,!1)))},{cache:em(s.pluralRules),strategy:_.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=el,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();class eg extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var eb=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(eb||{});function ey(...e){return e.filter(Boolean).join(".")}function ev(e){return ey(e.namespace,e.key)}function e_(e){console.error(e)}function eT(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eA(e,t){return m(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:_.variadic})}function eR(e,t){return eA((...t)=>new e(...t),t)}function eS(e){return{getDateTimeFormat:eR(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eR(Intl.NumberFormat,e.number),getPluralRules:eR(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eR(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eR(Intl.ListFormat,e.list),getDisplayNames:eR(Intl.DisplayNames,e.displayNames)}}function eP(e,t,r,n){let i=ey(n,r);if(!t)throw Error(i);let o=t;return r.split(".").forEach(t=>{let r=o[t];if(null==t||null==r)throw Error(i+` (${e})`);o=r}),o}let eH={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:2628e3,months:2628e3,quarter:7884e3,quarters:7884e3,year:31536e3,years:31536e3};var eO=r(687);let eN=(0,l.createContext)(void 0);function eB({children:e,formats:t,getMessageFallback:r,locale:n,messages:i,now:o,onError:a,timeZone:s}){let u=(0,l.useContext)(eN),c=(0,l.useMemo)(()=>u?.cache||eT(),[n,u?.cache]),h=(0,l.useMemo)(()=>u?.formatters||eS(c),[c,u?.formatters]),f=(0,l.useMemo)(()=>({...function({formats:e,getMessageFallback:t,messages:r,onError:n,...i}){return{...i,formats:e||void 0,messages:r||void 0,onError:n||e_,getMessageFallback:t||ev}}({locale:n,formats:void 0===t?u?.formats:t,getMessageFallback:r||u?.getMessageFallback,messages:void 0===i?u?.messages:i,now:o||u?.now,onError:a||u?.onError,timeZone:s||u?.timeZone}),formatters:h,cache:c}),[c,t,h,r,n,i,o,a,u,s]);return(0,eO.jsx)(eN.Provider,{value:f,children:e})}function eI(){let e=(0,l.useContext)(eN);if(!e)throw Error(void 0);return e}let eM=!1,eL="undefined"==typeof window;function eC(e){return function(e,t,r){let{cache:n,formats:i,formatters:o,getMessageFallback:a,locale:s,onError:u,timeZone:c}=eI(),h=e["!"],f="!"===t?void 0:t.slice((r+".").length);return c||eM||!eL||(eM=!0,u(new eg(eb.ENVIRONMENT_FALLBACK,void 0))),(0,l.useMemo)(()=>(function(e){let t=function(e,t,r,n=e_){try{if(!t)throw Error(void 0);let n=r?eP(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new eg(eb.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=ev,locale:i,messagesOrError:o,namespace:a,onError:s,timeZone:u}){let c=o instanceof eg;function h(e,t,r){let i=new eg(t,r);return s(i),n({error:i,key:e,namespace:a})}function f(s,f,p){var d;let m,E;if(c)return n({error:o,key:s,namespace:a});try{m=eP(i,o,s,a)}catch(e){return h(s,eb.MISSING_MESSAGE,e.message)}if("object"==typeof m){let e;return h(s,Array.isArray(m)?eb.INVALID_MESSAGE:eb.INSUFFICIENT_PATH,e)}let g=(d=m,f?void 0:d);if(g)return g;r.getMessageFormat||(r.getMessageFormat=eA((...e)=>new eE(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{E=r.getMessageFormat(m,i,function(e,t,r){let n=eE.formats.date,i=eE.formats.time,o={...e?.dateTime,...t?.dateTime},a={date:{...n,...o},time:{...i,...o},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,p,u),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:u,...t})}})}catch(e){return h(s,eb.INVALID_MESSAGE,e.message)}try{let e=E.format(f?function(e){let t={};return Object.keys(e).forEach(r=>{let n,i=0,o=e[r];n="function"==typeof o?e=>{let t=o(e);return(0,l.isValidElement)(t)?(0,l.cloneElement)(t,{key:r+i++}):t}:o,t[r]=n}),t}(f):f);if(null==e)throw Error(void 0);return(0,l.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return h(s,eb.FORMATTING_ERROR,e.message)}}function p(e,t,r){let n=f(e,t,r);return"string"!=typeof n?h(e,eb.INVALID_MESSAGE,void 0):n}return p.rich=f,p.markup=(e,t,r)=>f(e,t,r),p.raw=e=>{if(c)return n({error:o,key:e,namespace:a});try{return eP(i,o,e,a)}catch(t){return h(e,eb.MISSING_MESSAGE,t.message)}},p.has=e=>{if(c)return!1;try{return eP(i,o,e,a),!0}catch{return!1}},p}({...e,messagesOrError:t})})({cache:n,formatters:o,getMessageFallback:a,messages:h,namespace:f,onError:u,formats:i,locale:s,timeZone:c}),[n,o,a,h,f,u,i,s,c])}({"!":eI().messages},e?`!.${e}`:"!","!")}function ew(){return eI().locale}function eD(){let{formats:e,formatters:t,locale:r,now:n,onError:i,timeZone:o}=eI();return(0,l.useMemo)(()=>(function(e){let{_cache:t=eT(),_formatters:r=eS(t),formats:n,locale:i,onError:o=e_,timeZone:a}=e;function s(e){return e?.timeZone||(a?e={...e,timeZone:a}:o(new eg(eb.ENVIRONMENT_FALLBACK,void 0))),e}function u(e,t,r,n,i){let a;try{a=function(e,t,r){let n;if("string"==typeof t){if(!(n=e?.[t])){let e=new eg(eb.MISSING_FORMAT,void 0);throw o(e),e}}else n=t;return r&&(n={...n,...r}),n}(r,e,t)}catch{return i()}try{return n(a)}catch(e){return o(new eg(eb.FORMATTING_ERROR,e.message)),i()}}function c(e,t,o){return u(t,o,n?.dateTime,t=>(t=s(t),r.getDateTimeFormat(i,t).format(e)),()=>String(e))}function l(){return e.now?e.now:(o(new eg(eb.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:c,number:function(e,t,o){return u(t,o,n?.number,t=>r.getNumberFormat(i,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let o,a,s={};t instanceof Date||"number"==typeof t?o=new Date(t):t&&(o=null!=t.now?new Date(t.now):l(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),o||(o=l());let u=(new Date(e).getTime()-o.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<2628e3?"week":t<31536e3?"month":"year"}(u)),s.numeric="second"===a?"auto":"always";let c=(n=a,Math.round(u/eH[n]));return r.getRelativeTimeFormat(i,s).format(c,a)}catch(t){return o(new eg(eb.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t,o){let a=[],s=new Map,c=0;for(let t of e){let e;"object"==typeof t?(e=String(c),s.set(e,t)):e=String(t),a.push(e),c++}return u(t,o,n?.list,e=>{let t=r.getListFormat(i,e).formatToParts(a).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,o,a){return u(o,a,n?.dateTime,n=>(n=s(n),r.getDateTimeFormat(i,n).formatRange(e,t)),()=>[c(e),c(t)].join(" – "))}}})({formats:e,locale:r,now:n,onError:i,timeZone:o,_formatters:t}),[e,t,n,r,i,o])}},8946:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var n=r(1120),i=r(9769);let o=(0,n.cache)(async function(e){return(await (0,i.A)(e)).now}),a=(0,n.cache)(async function(){return(await (0,i.A)()).formats});var s=r(994),u=r(7413);let c=(0,n.cache)(async function(e){return(await (0,i.A)(e)).timeZone});async function l(e){return c(e?.locale)}var h=r(3930);let f=(0,n.cache)(async function(){return(await (0,i.A)()).locale});async function p({formats:e,locale:t,messages:r,now:n,timeZone:i,...c}){return(0,u.jsx)(s.default,{formats:void 0===e?await a():e,locale:t??await f(),messages:void 0===r?await (0,h.A)():r,now:n??await o(),timeZone:i??await l(),...c})}},8976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9769:(e,t,r)=>{"use strict";r.d(t,{A:()=>y});var n=r(1120),i=r(4604);function o(e){return e.includes("[[...")}function a(e){return e.includes("[...")}function s(e){return e.includes("[")}function u(e){return"function"==typeof e.then}r(9933);var c=r(6280);r(6294);let l=(0,n.cache)(function(){return{locale:void 0}}),h=(0,n.cache)(async function(){let e=(0,c.b)();return u(e)?await e:e}),f=(0,n.cache)(async function(){let e;try{e=(await h()).get("X-NEXT-INTL-LOCALE")||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function p(){return l().locale||await f()}var d=r(6140);let m=(0,n.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),E=(0,n.cache)(async function(e,t){let r=e({locale:t,get requestLocale(){return t?Promise.resolve(t):p()}});if(u(r)&&(r=await r),!r.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return r}),g=(0,n.cache)(i.b),b=(0,n.cache)(i.d),y=(0,n.cache)(async function(e){let t=await E(d.Ay,e);return{...(0,i.i)(t),_formatters:g(b()),timeZone:t.timeZone||m()}})},9916:(e,t,r)=>{"use strict";var n=r(7576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}})},9933:(e,t,r)=>{"use strict";let n=r(4069),i=r(3158),o=r(9294),a=r(3033),s=r(4971),u=r(23),c=r(8388),l=r(6926),h=(r(4523),r(8719)),f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):g.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):b.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(E);function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function g(){return this.getAll().map(e=>[e.name,e]).values()}function b(e){for(let e of this.getAll())this.delete(e.name);return e}}};