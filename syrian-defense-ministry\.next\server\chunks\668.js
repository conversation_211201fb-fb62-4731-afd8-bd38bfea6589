exports.id=668,exports.ids=[668],exports.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(1042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return u},urlObjectKeys:function(){return a}});let n=r(740)._(r(6715)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",u=e.hash||"",s=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?"+s||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==l?(l="//"+(l||""),a&&"/"!==a[0]&&(a="/"+a)):l||(l=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+i+l+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return i(e)}},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NavigationResultTag:function(){return h},PrefetchPriority:function(){return f},cancelPrefetchTask:function(){return s},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return a},navigate:function(){return o},prefetch:function(){return n},reschedulePrefetchTask:function(){return l},revalidateEntireCache:function(){return i},schedulePrefetchTask:function(){return u}});let r=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},n=r,o=r,i=r,a=r,u=r,s=r,l=r,c=r;var h=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),f=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return l},getSelectedParams:function(){return function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),a=i?t[1]:t;!a||a.startsWith(o.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r}}});let n=r(2859),o=r(3913),i=r(4077),a=e=>"/"===e[0]?e.slice(1):e,u=e=>"string"==typeof e?"children"===e?"":e:e[1];function s(e){return e.reduce((e,t)=>""===(t=a(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function l(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===o.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(o.PAGE_SEGMENT_KEY))return"";let i=[u(r)],a=null!=(t=e[1])?t:{},c=a.children?l(a.children):void 0;if(void 0!==c)i.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=l(t);void 0!==r&&i.push(r)}return s(i)}function c(e,t){let r=function e(t,r){let[o,a]=t,[s,c]=r,h=u(o),f=u(s);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>h.startsWith(e)||f.startsWith(e)))return"";if(!(0,i.matchSegment)(o,s)){var d;return null!=(d=l(r))?d:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return u(s)+"/"+r}return null}(e,t);return null==r||"/"===r?r:s(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},994:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\node_modules\\\\next-intl\\\\dist\\\\esm\\\\production\\\\shared\\\\NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\node_modules\\next-intl\\dist\\esm\\production\\shared\\NextIntlClientProvider.js","default")},1042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,s.isDynamicServerError)(t)||(0,u.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(8388),o=r(2637),i=r(1846),a=r(1162),u=r(4971),s=r(8479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=r(4722),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function a(e){let t,r,i;for(let n of e.split("/"))if(r=o.find(e=>n.startsWith(e))){[t,i]=e.split(r,2);break}if(!t||!r||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":i="/"===t?"/"+i:t+"/"+i;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=t.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:i}}},1500:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,i,a,u,s,l){if(0===Object.keys(a[1]).length){r.head=s;return}for(let c in a[1]){let h,f=a[1][c],d=f[0],p=(0,n.createRouterCacheKey)(d),m=null!==u&&void 0!==u[2][c]?u[2][c]:null;if(i){let n=i.parallelRoutes.get(c);if(n){let i,a=(null==l?void 0:l.kind)==="auto"&&l.status===o.PrefetchCacheEntryStatus.reusable,u=new Map(n),h=u.get(p);i=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),navigatedAt:t}:a&&h?{lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),loading:null,navigatedAt:t},u.set(p,i),e(t,i,h,f,m||null,s,l),r.parallelRoutes.set(c,u);continue}}if(null!==m){let e=m[1],r=m[3];h={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else h={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=r.parallelRoutes.get(c);g?g.set(p,h):r.parallelRoutes.set(c,new Map([[p,h]])),e(t,h,void 0,f,m,s,l)}}}});let n=r(3123),o=r(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return f},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return d}});let n=r(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),i=r(6341),a=r(4396),u=r(660),s=r(4722),l=r(2958),c=r(5499);function h(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,u.djb2Hash)(t).toString(36).slice(0,6)),r}function f(e,t,r){let n=(0,s.normalizeAppPath)(e),u=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,i.interpolateDynamicPath)(n,t,u),{name:f,ext:d}=o.default.parse(r),p=h(o.default.posix.join(e,f)),m=p?`-${p}`:"";return(0,l.normalizePathSep)(o.default.join(c,`${f}${m}${d}`))}function d(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=h(e),!t.endsWith("/route")){let{dir:e,name:n,ext:i}=o.default.parse(t);t=o.default.posix.join(e,`${n}${r?`-${r}`:""}${i}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,o=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${o}`)+(r?"/route":"")}},1794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(9289),o=r(6736);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],o=r[0];if(Array.isArray(n)&&Array.isArray(o)){if(n[0]!==o[0]||n[2]!==o[2])return!0}else if(n!==o)return!0;if(t[4])return!r[4];if(r[4])return!0;let i=Object.values(t[1])[0],a=Object.values(r[1])[0];return!i||!a||e(i,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(1550);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},2308:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,o,,a]=t;for(let u in n.includes(i.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),o)e(o[u],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(6928),o=r(9008),i=r(3913);async function a(e){let t=new Set;await u({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function u(e){let{navigatedAt:t,state:r,updatedTree:i,updatedCache:a,includeNextUrl:s,fetchedSegments:l,rootTree:c=i,canonicalUrl:h}=e,[,f,d,p]=i,m=[];if(d&&d!==h&&"refresh"===p&&!l.has(d)){l.add(d);let e=(0,o.fetchServerResponse)(new URL(d,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:s?r.nextUrl:null}).then(e=>{let{flightData:r}=e;if("string"!=typeof r)for(let e of r)(0,n.applyFlightData)(t,a,a,e)});m.push(e)}for(let e in f){let n=u({navigatedAt:t,state:r,updatedTree:f[e],updatedCache:a,includeNextUrl:s,fetchedSegments:l,rootTree:c,canonicalUrl:h});m.push(n)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let n=r(5362);function o(e,t){let r=[],o=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,r);return(e,n)=>{if("string"!=typeof e)return!1;let o=i(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete o.params[e.name];return{...n,...o.params}}}},2584:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return o}});let n=r(3763);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,o);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==a)return n.ReflectAdapter.get(t,a,o)},set(t,r,o,i){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,o,i);let a=r.toLowerCase(),u=Object.keys(e).find(e=>e.toLowerCase()===a);return n.ReflectAdapter.set(t,u??r,o,i)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==i&&n.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===i||n.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},2765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(3210);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},3406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{IDLE_LINK_STATUS:function(){return l},PENDING_LINK_STATUS:function(){return s},mountFormInstance:function(){return E},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return _},onNavigationIntent:function(){return v},pingVisibleLinks:function(){return T},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return h},unmountPrefetchableInstance:function(){return b}}),r(3690);let n=r(9752),o=r(9154),i=r(593),a=r(3210),u=null,s={pending:!0},l={pending:!1};function c(e){(0,a.startTransition)(()=>{null==u||u.setOptimisticLinkStatus(l),null==e||e.setOptimisticLinkStatus(s),u=e})}function h(e){u===e&&(u=null)}let f="function"==typeof WeakMap?new WeakMap:new Map,d=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;_(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==f.get(e)&&b(e),f.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,n.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,r,n,o,i){if(o){let o=g(t);if(null!==o){let t={router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:i};return m(e,t),t}}return{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:i}}function E(e,t,r,n){let o=g(t);null!==o&&m(e,{router:r,kind:n,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=f.get(e);if(void 0!==t){f.delete(e),d.delete(t);let r=t.prefetchTask;null!==r&&(0,i.cancelPrefetchTask)(r)}null!==p&&p.unobserve(e)}function _(e,t){let r=f.get(e);void 0!==r&&(r.isVisible=t,t?d.add(r):d.delete(r),R(r))}function v(e,t){let r=f.get(e);void 0!==r&&void 0!==r&&(r.wasHoveredOrTouched=!0,R(r))}function R(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,i.cancelPrefetchTask)(t);return}}function T(e,t){let r=(0,i.getCurrentCacheVersion)();for(let n of d){let a=n.prefetchTask;if(null!==a&&n.cacheVersion===r&&a.key.nextUrl===e&&a.treeAtTimeOfPrefetch===t)continue;null!==a&&(0,i.cancelPrefetchTask)(a);let u=(0,i.createCacheKey)(n.prefetchHref,e),s=n.wasHoveredOrTouched?i.PrefetchPriority.Intent:i.PrefetchPriority.Default;n.prefetchTask=(0,i.schedulePrefetchTask)(u,t,n.kind===o.PrefetchKind.FULL,s),n.cacheVersion=(0,i.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3690:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return E},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let n=r(9154),o=r(8830),i=r(3210),a=r(1992);r(593);let u=r(9129),s=r(6127),l=r(9752),c=r(5076),h=r(3406);function f(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?d({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:n.ACTION_REFRESH,origin:window.location.origin},t)))}async function d(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;t.pending=r;let i=r.payload,u=t.action(o,i);function s(e){r.discarded||(t.state=e,f(t,n),r.resolve(e))}(0,a.isThenable)(u)?u.then(s,e=>{f(t,n),r.reject(e)}):s(u)}function p(e,t){let r={state:e,dispatch:(e,t)=>(function(e,t,r){let o={resolve:r,reject:()=>{}};if(t.type!==n.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,i.startTransition)(()=>{r(e)})}let a={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=a,d({actionQueue:e,action:a,setState:r})):t.type===n.ACTION_NAVIGATE||t.type===n.ACTION_RESTORE?(e.pending.discarded=!0,a.next=e.pending.next,e.pending.payload.type===n.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),d({actionQueue:e,action:a,setState:r})):(null!==e.last&&(e.last.next=a),e.last=a)})(r,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return r}function m(){return null}function g(){return null}function y(e,t,r,o){let i=new URL((0,s.addBasePath)(e),location.href);(0,h.setLinkForCurrentNavigation)(o);(0,u.dispatchAppRouterAction)({type:n.ACTION_NAVIGATE,url:i,isExternalUrl:(0,l.isExternalURL)(i),locationSearch:location.search,shouldScroll:r,navigateType:t,allowAliasing:!0})}function E(e,t){(0,u.dispatchAppRouterAction)({type:n.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),o=(0,l.createPrefetchURL)(e);if(null!==o){var i;(0,c.prefetchReducer)(r.state,{type:n.ACTION_PREFETCH,url:o,kind:null!=(i=null==t?void 0:t.kind)?i:n.PrefetchKind.FULL})}},replace:(e,t)=>{(0,i.startTransition)(()=>{var r;y(e,"replace",null==(r=null==t?void 0:t.scroll)||r,null)})},push:(e,t)=>{(0,i.startTransition)(()=>{var r;y(e,"push",null==(r=null==t?void 0:t.scroll)||r,null)})},refresh:()=>{(0,i.startTransition)(()=>{(0,u.dispatchAppRouterAction)({type:n.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),r(4827);let n=r(2785);function o(e,t,r){void 0===r&&(r=!0);let o=new URL("http://n"),i=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:a,searchParams:u,search:s,hash:l,href:c,origin:h}=new URL(e,i);if(h!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:r?(0,n.searchParamsToUrlQuery)(u):void 0,search:s,hash:l,href:c.slice(h.length)}}},3898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillCacheWithNewSubTreeData:function(){return s},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return l}});let n=r(4400),o=r(1500),i=r(3123),a=r(3913);function u(e,t,r,u,s,l){let{segmentPath:c,seedData:h,tree:f,head:d}=u,p=t,m=r;for(let t=0;t<c.length;t+=2){let r=c[t],u=c[t+1],g=t===c.length-2,y=(0,i.createRouterCacheKey)(u),E=m.parallelRoutes.get(r);if(!E)continue;let b=p.parallelRoutes.get(r);b&&b!==E||(b=new Map(E),p.parallelRoutes.set(r,b));let _=E.get(y),v=b.get(y);if(g){if(h&&(!v||!v.lazyData||v===_)){let t=h[0],r=h[1],i=h[3];v={lazyData:null,rsc:l||t!==a.PAGE_SEGMENT_KEY?r:null,prefetchRsc:null,head:null,prefetchHead:null,loading:i,parallelRoutes:l&&_?new Map(_.parallelRoutes):new Map,navigatedAt:e},_&&l&&(0,n.invalidateCacheByRouterState)(v,_,f),l&&(0,o.fillLazyItemsTillLeafWithHead)(e,v,_,f,h,d,s),b.set(y,v)}continue}v&&_&&(v===_&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},b.set(y,v)),p=v,m=_)}}function s(e,t,r,n,o){u(e,t,r,n,o,!0)}function l(e,t,r,n,o){u(e,t,r,n,o,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3930:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(1120),o=r(9769);let i=(0,n.cache)(async function(e){var t=await (0,o.A)(e);if(!t.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return t.messages});async function a(e){return i(e?.locale)}},4069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return u},RequestCookiesAdapter:function(){return s},appendMutableCookies:function(){return h},areCookiesMutableInCurrentPhase:function(){return p},getModifiedCookieValues:function(){return c},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return d}});let n=r(3158),o=r(3763),i=r(9294),a=r(3033);class u extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new u}}class s{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return u.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function c(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function h(e,t){let r=c(t);if(0===r.length)return!1;let o=new n.ResponseCookies(e),i=o.getAll();for(let e of r)o.set(e);for(let e of i)o.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],u=new Set,s=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>u.has(e.name)),t){let e=[];for(let t of a){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case l:return a;case"delete":return function(...t){u.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{s()}};case"set":return function(...t){u.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{s()}};default:return o.ReflectAdapter.get(e,t,r)}}});return c}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return o.ReflectAdapter.get(e,r,n)}}});return t}function p(e){return"action"===e.phase}function m(e){if(!p((0,a.getExpectedRequestStore)(e)))throw new u}function g(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return m},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return h},parseParameter:function(){return s}});let n=r(6143),o=r(1437),i=r(3293),a=r(2887),u=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function s(e){let t=e.match(u);return t?l(t[2]):l(e)}function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},s=1,c=[];for(let h of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>h.startsWith(e)),a=h.match(u);if(e&&a&&a[2]){let{key:t,optional:r,repeat:o}=l(a[2]);n[t]={pos:s++,repeat:o,optional:r},c.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:o}=l(a[2]);n[e]={pos:s++,repeat:t,optional:o},r&&a[1]&&c.push("/"+(0,i.escapeStringRegexp)(a[1]));let u=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(u=u.substring(1)),c.push(u)}else c.push("/"+(0,i.escapeStringRegexp)(h));t&&a&&a[3]&&c.push((0,i.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:n}}function h(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:i,groups:a}=c(e,r,n),u=i;return o||(u+="(?:/)?"),{re:RegExp("^"+u+"$"),groups:a}}function f(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:o,routeKeys:a,keyPrefix:u,backreferenceDuplicateKeys:s}=e,{key:c,optional:h,repeat:f}=l(o),d=c.replace(/\W/g,"");u&&(d=""+u+d);let p=!1;(0===d.length||d.length>30)&&(p=!0),isNaN(parseInt(d.slice(0,1)))||(p=!0),p&&(d=n());let m=d in a;u?a[d]=""+u+c:a[d]=c;let g=r?(0,i.escapeStringRegexp)(r):"";return t=m&&s?"\\k<"+d+">":f?"(?<"+d+">.+?)":"(?<"+d+">[^/]+?)",h?"(?:/"+g+t+")?":"/"+g+t}function d(e,t,r,s,l){let c,h=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),d={},p=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(u);if(e&&a&&a[2])p.push(f({getSafeRouteKey:h,interceptionMarker:a[1],segment:a[2],routeKeys:d,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:l}));else if(a&&a[2]){s&&a[1]&&p.push("/"+(0,i.escapeStringRegexp)(a[1]));let e=f({getSafeRouteKey:h,segment:a[2],routeKeys:d,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:l});s&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,i.escapeStringRegexp)(c));r&&a&&a[3]&&p.push((0,i.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:d}}function p(e,t){var r,n,o;let i=d(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(o=t.backreferenceDuplicateKeys)&&o),a=i.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...h(e,t),namedRegex:"^"+a+"$",routeKeys:i.routeKeys}}function m(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=d(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},4397:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let n=r(3123);function o(e,t){return function e(t,r,o){if(0===Object.keys(r).length)return[t,o];if(r.children){let[i,a]=r.children,u=t.parallelRoutes.get("children");if(u){let t=(0,n.createRouterCacheKey)(i),r=u.get(t);if(r){let n=e(r,a,o+"/"+t);if(n)return n}}}for(let i in r){if("children"===i)continue;let[a,u]=r[i],s=t.parallelRoutes.get(i);if(!s)continue;let l=(0,n.createRouterCacheKey)(a),c=s.get(l);if(!c)continue;let h=e(c,u,o+"/"+l);if(h)return h}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let n=r(3123);function o(e,t,r){for(let o in r[1]){let i=r[1][o][0],a=(0,n.createRouterCacheKey)(i),u=t.parallelRoutes.get(o);if(u){let t=new Map(u);t.delete(a),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4604:(e,t,r)=>{"use strict";r.d(t,{b:()=>eA,d:()=>eR,e:()=>eS,f:()=>e_,g:()=>ev,i:()=>eN,r:()=>eM});var n,o,i,a,u,s,l,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function d(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function p(e,t){var r=t&&t.cache?t.cache:b,n=t&&t.serializer?t.serializer:y;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?m:g;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function m(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function g(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}Object.create,"function"==typeof SuppressedError&&SuppressedError;var y=function(){return JSON.stringify(arguments)},E=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),b={create:function(){return new E}},_={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,g.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,m.bind(this,e,r,n)}};function v(e){return e.type===o.literal}function R(e){return e.type===o.number}function T(e){return e.type===o.date}function P(e){return e.type===o.time}function A(e){return e.type===o.select}function O(e){return e.type===o.plural}function S(e){return e.type===o.tag}function M(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function N(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var H=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,C=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,I=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,w=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,L=/^(@+)?(\+|#+)?[rs]?$/g,B=/(\*)(0+)|(#+)(0+)|(0+)/g,j=/^(0+)$/;function U(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(L,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function D(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function x(e){var t=D(e);return t||{}}var F={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},k=new RegExp("^".concat(H.source,"*")),G=new RegExp("".concat(H.source,"*$"));function V(e,t){return{start:e,end:t}}var K=!!String.prototype.startsWith&&"_a".startsWith("a",1),X=!!String.fromCodePoint,z=!!Object.fromEntries,$=!!String.prototype.codePointAt,W=!!String.prototype.trimStart,q=!!String.prototype.trimEnd,Y=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},Z=!0;try{Z=(null==(a=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){Z=!1}var Q=K?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},J=X?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},ee=z?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},et=$?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},er=W?function(e){return e.trimStart()}:function(e){return e.replace(k,"")},en=q?function(e){return e.trimEnd()}:function(e){return e.replace(G,"")};function eo(e,t){return new RegExp(e,t)}if(Z){var ei=eo("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");u=function(e,t){var r;return ei.lastIndex=t,null!=(r=ei.exec(e)[1])?r:""}}else u=function(e,t){for(var r=[];;){var n,o=et(e,t);if(void 0===o||es(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return J.apply(void 0,r)};var ea=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var u=this.parseArgument(e,r);if(u.err)return u;i.push(u.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var s=this.clonePosition();this.bump(),i.push({type:o.pound,location:V(s,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&eu(this.peek()||0)){var u=this.parseTag(e,t);if(u.err)return u;i.push(u.val)}else{var u=this.parseLiteral(e,t);if(u.err)return u;i.push(u.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,V(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:V(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,V(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var u=a.val,s=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,V(r,this.clonePosition()));if(this.isEOF()||!eu(this.char()))return this.error(n.INVALID_TAG,V(s,this.clonePosition()));var l=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,V(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:u,location:V(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,V(s,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var u=this.tryParseLeftAngleBracket();if(u){n+=u;continue}break}var s=V(r,this.clonePosition());return{val:{type:o.literal,value:n,location:s},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(eu(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return J.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),J(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,V(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,V(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:V(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,V(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=u(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:V(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var u,s=this.clonePosition(),l=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(l){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,V(s,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var d=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=en(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,V(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:V(d,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var y=V(a,this.clonePosition());if(h&&Q(null==h?void 0:h.style,"::",0)){var E=er(h.style.slice(2));if("number"===l){var p=this.parseNumberSkeletonFromString(E,h.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:y,style:p.val},err:null}}if(0===E.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,y);var b,_=E;this.locale&&(_=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),u=i<2?1:3+(i>>1),s=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(F[t||""]||F[n||""]||F["".concat(n,"-001")]||F["001"])[0]}(t);for(("H"==s||"k"==s)&&(u=0);u-- >0;)r+="a";for(;a-- >0;)r=s+r}else"J"===o?r+="H":r+=o}return r}(E,this.locale));var m={type:i.dateTime,pattern:_,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(b={},_.replace(C,function(e){var t=e.length;switch(e[0]){case"G":b.era=4===t?"long":5===t?"narrow":"short";break;case"y":b.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":b.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":b.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":b.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"a":b.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":b.hourCycle="h12",b.hour=["numeric","2-digit"][t-1];break;case"H":b.hourCycle="h23",b.hour=["numeric","2-digit"][t-1];break;case"K":b.hourCycle="h11",b.hour=["numeric","2-digit"][t-1];break;case"k":b.hourCycle="h24",b.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":b.minute=["numeric","2-digit"][t-1];break;case"s":b.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":b.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),b):{}};return{val:{type:"date"===l?o.date:o.time,value:r,location:y,style:m},err:null}}return{val:{type:"number"===l?o.number:"date"===l?o.date:o.time,value:r,location:y,style:null!=(u=null==h?void 0:h.style)?u:null},err:null};case"plural":case"selectordinal":case"select":var v=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,V(v,f({},v)));this.bumpSpace();var R=this.parseIdentifierIfPossible(),T=0;if("select"!==l&&"offset"===R.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,V(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),R=this.parseIdentifierIfPossible(),T=p.val}var P=this.tryParsePluralOrSelectOptions(e,l,t,R);if(P.err)return P;var g=this.tryParseArgumentClose(a);if(g.err)return g;var A=V(a,this.clonePosition());if("select"===l)return{val:{type:o.select,value:r,options:ee(P.val),location:A},err:null};return{val:{type:o.plural,value:r,options:ee(P.val),offset:T,pluralType:"plural"===l?"cardinal":"ordinal",location:A},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,V(s,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,V(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,V(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(I).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),u=0;u<a.length;u++)if(0===a[u].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),x(t))},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),x(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(B,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(j.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(w.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(w,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),U(o)));continue}if(L.test(n.stem)){t=f(f({},t),U(n.stem));continue}var i=D(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!j.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,u=[],s=new Set,l=o.value,c=o.location;;){if(0===l.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=V(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(s.has(l))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===l&&(a=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,V(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(d);if(m.err)return m;u.push([l,{value:p.val,location:V(d,this.clonePosition())}]),s.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===u.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,V(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,V(this.clonePosition(),this.clonePosition())):{val:u,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var u=V(n,this.clonePosition());return o?Y(i*=r)?{val:i,err:null}:this.error(t,u):this.error(e,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=et(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Q(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&es(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function eu(e){return e>=97&&e<=122||e>=65&&e<=90}function es(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function el(e,t){void 0===t&&(t={});var r=new ea(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,A(t)||O(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else R(t)&&M(t.style)||(T(t)||P(t))&&N(t.style)?delete t.style.location:S(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(s||(s={}));var ec=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),eh=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),s.INVALID_VALUE,o)||this}return h(t,e),t}(ec),ef=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),s.INVALID_VALUE,n)||this}return h(t,e),t}(ec),ed=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),s.MISSING_VALUE,r)||this}return h(t,e),t}(ec);function ep(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(l||(l={}));var em=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var a,u,c=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=c.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===l.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,i,a,u,c){if(1===t.length&&v(t[0]))return[{type:l.literal,value:t[0].value}];for(var h=[],f=0;f<t.length;f++){var d=t[f];if(v(d)){h.push({type:l.literal,value:d.value});continue}if(d.type===o.pound){"number"==typeof u&&h.push({type:l.literal,value:n.getNumberFormat(r).format(u)});continue}var p=d.value;if(!(a&&p in a))throw new ed(p,c);var m=a[p];if(d.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),h.push({type:"string"==typeof m?l.literal:l.object,value:m});continue}if(T(d)){var g="string"==typeof d.style?i.date[d.style]:N(d.style)?d.style.parsedOptions:void 0;h.push({type:l.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(P(d)){var g="string"==typeof d.style?i.time[d.style]:N(d.style)?d.style.parsedOptions:i.time.medium;h.push({type:l.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(R(d)){var g="string"==typeof d.style?i.number[d.style]:M(d.style)?d.style.parsedOptions:void 0;g&&g.scale&&(m*=g.scale||1),h.push({type:l.literal,value:n.getNumberFormat(r,g).format(m)});continue}if(S(d)){var y=d.children,E=d.value,b=a[E];if("function"!=typeof b)throw new ef(E,"function",c);var _=b(e(y,r,n,i,a,u).map(function(e){return e.value}));Array.isArray(_)||(_=[_]),h.push.apply(h,_.map(function(e){return{type:"string"==typeof e?l.literal:l.object,value:e}}))}if(A(d)){var H=d.options[m]||d.options.other;if(!H)throw new eh(d.value,m,Object.keys(d.options),c);h.push.apply(h,e(H.value,r,n,i,a));continue}if(O(d)){var H=d.options["=".concat(m)];if(!H){if(!Intl.PluralRules)throw new ec('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',s.MISSING_INTL_API,c);var C=n.getPluralRules(r,{type:d.pluralType}).select(m-(d.offset||0));H=d.options[C]||d.options.other}if(!H)throw new eh(d.value,m,Object.keys(d.options),c);h.push.apply(h,e(H.value,r,n,i,a,m-(d.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===l.literal&&t.type===l.literal?r.value+=t.value:e.push(t),e},[])}(c.ast,c.locales,c.formatters,c.formats,e,void 0,c.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=c.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(c.locales)[0]}},this.getAst=function(){return c.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=i||{},m=(h.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(h,["formatters"]));this.ast=e.__parse(t,f(f({},m),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,o;return e[t]=(r=a[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},a)):a),this.formatters=i&&i.formatters||(void 0===(u=this.formatterCache)&&(u={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ep(u.number),strategy:_.variadic}),getDateTimeFormat:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,d([void 0],t,!1)))},{cache:ep(u.dateTime),strategy:_.variadic}),getPluralRules:p(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,d([void 0],t,!1)))},{cache:ep(u.pluralRules),strategy:_.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=el,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}(),eg=r(1120);class ey extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var eE=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(eE||{});function eb(...e){return e.filter(Boolean).join(".")}function e_(e){return eb(e.namespace,e.key)}function ev(e){console.error(e)}function eR(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eT(e,t){return p(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:_.variadic})}function eP(e,t){return eT((...t)=>new e(...t),t)}function eA(e){return{getDateTimeFormat:eP(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eP(Intl.NumberFormat,e.number),getPluralRules:eP(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eP(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eP(Intl.ListFormat,e.list),getDisplayNames:eP(Intl.DisplayNames,e.displayNames)}}function eO(e,t,r,n){let o=eb(n,r);if(!t)throw Error(o);let i=t;return r.split(".").forEach(t=>{let r=i[t];if(null==t||null==r)throw Error(o+` (${e})`);i=r}),i}function eS(e){let t=function(e,t,r,n=ev){try{if(!t)throw Error(void 0);let n=r?eO(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new ey(eE.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=e_,locale:o,messagesOrError:i,namespace:a,onError:u,timeZone:s}){let l=i instanceof ey;function c(e,t,r){let o=new ey(t,r);return u(o),n({error:o,key:e,namespace:a})}function h(u,h,f){var d;let p,m;if(l)return n({error:i,key:u,namespace:a});try{p=eO(o,i,u,a)}catch(e){return c(u,eE.MISSING_MESSAGE,e.message)}if("object"==typeof p){let e;return c(u,Array.isArray(p)?eE.INVALID_MESSAGE:eE.INSUFFICIENT_PATH,e)}let g=(d=p,h?void 0:d);if(g)return g;r.getMessageFormat||(r.getMessageFormat=eT((...e)=>new em(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{m=r.getMessageFormat(p,o,function(e,t,r){let n=em.formats.date,o=em.formats.time,i={...e?.dateTime,...t?.dateTime},a={date:{...n,...i},time:{...o,...i},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,f,s),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:s,...t})}})}catch(e){return c(u,eE.INVALID_MESSAGE,e.message)}try{let e=m.format(h?function(e){let t={};return Object.keys(e).forEach(r=>{let n,o=0,i=e[r];n="function"==typeof i?e=>{let t=i(e);return(0,eg.isValidElement)(t)?(0,eg.cloneElement)(t,{key:r+o++}):t}:i,t[r]=n}),t}(h):h);if(null==e)throw Error(void 0);return(0,eg.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return c(u,eE.FORMATTING_ERROR,e.message)}}function f(e,t,r){let n=h(e,t,r);return"string"!=typeof n?c(e,eE.INVALID_MESSAGE,void 0):n}return f.rich=h,f.markup=(e,t,r)=>h(e,t,r),f.raw=e=>{if(l)return n({error:i,key:e,namespace:a});try{return eO(o,i,e,a)}catch(t){return c(e,eE.MISSING_MESSAGE,t.message)}},f.has=e=>{if(l)return!1;try{return eO(o,i,e,a),!0}catch{return!1}},f}({...e,messagesOrError:t})}function eM(e,t){return e===t?void 0:e.slice((t+".").length)}function eN({formats:e,getMessageFallback:t,messages:r,onError:n,...o}){return{...o,formats:e||void 0,messages:r||void 0,onError:n||ev,getMessageFallback:t||e_}}},4642:(e,t)=>{"use strict";function r(e){let t=parseInt(e.slice(0,2),16),r=t>>1&63,n=Array(6);for(let e=0;e<6;e++){let t=r>>5-e&1;n[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:n,hasRestArgs:1==(1&t)}}function n(e,t){let r=Array(e.length);for(let n=0;n<e.length;n++)(n<6&&t.usedArgs[n]||n>=6&&t.hasRestArgs)&&(r[n]=e[n]);return r}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{extractInfoFromServerReferenceId:function(){return r},omitUnusedArgs:function(){return n}})},4674:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}});let n=r(4949),o=r(1550),i=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:i}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+i};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=r(5531),o=r(5499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return E},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return s},getLocationOrigin:function(){return a},getURL:function(){return u},isAbsoluteUrl:function(){return i},isResSent:function(){return l},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=a();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,d=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class E extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},4949:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},5076:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return i},prefetchReducer:function(){return a}});let n=r(5144),o=r(5334),i=new n.PromiseQueue(5),a=function(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return(0,o.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return l}});let n=r(6312),o=r(9656);var i=o._("_maxConcurrency"),a=o._("_runningCount"),u=o._("_queue"),s=o._("_processNext");class l{enqueue(e){let t,r,o=new Promise((e,n)=>{t=e,r=n}),i=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,s)[s]()}};return n._(this,u)[u].push({promiseFn:o,task:i}),n._(this,s)[s](),o}bump(e){let t=n._(this,u)[u].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,u)[u].splice(t,1)[0];n._(this,u)[u].unshift(e),n._(this,s)[s](!0)}}constructor(e=5){Object.defineProperty(this,s,{value:c}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,u,{writable:!0,value:void 0}),n._(this,i)[i]=e,n._(this,a)[a]=0,n._(this,u)[u]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,i)[i]||e)&&n._(this,u)[u].length>0){var t;null==(t=n._(this,u)[u].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5196:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(8521),o=r(687);function i({locale:e,...t}){if(!e)throw Error(void 0);return(0,o.jsx)(n.Dk,{locale:e,...t})}},5232:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,r){let{url:v,isExternalUrl:R,navigateType:T,shouldScroll:P,allowAliasing:A}=r,O={},{hash:S}=v,M=(0,o.createHrefFromUrl)(v),N="push"===T;if((0,g.prunePrefetchCache)(t.prefetchCache),O.preserveCustomHistoryState=!1,O.pendingPush=N,R)return b(t,O,v.toString(),N);if(document.getElementById("__next-page-redirect"))return b(t,O,M,N);let H=(0,g.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:A}),{treeAtTimeOfPrefetch:C,data:I}=H;return f.prefetchQueue.bump(I),I.then(f=>{let{flightData:g,canonicalUrl:R,postponed:T}=f,A=Date.now(),I=!1;if(H.lastUsedTime||(H.lastUsedTime=A,I=!0),H.aliased){let n=(0,E.handleAliasedPrefetchEntry)(A,t,g,v,O);return!1===n?e(t,{...r,allowAliasing:!1}):n}if("string"==typeof g)return b(t,O,g,N);let w=R?(0,o.createHrefFromUrl)(R):M;if(S&&t.canonicalUrl.split("#",1)[0]===w.split("#",1)[0])return O.onlyHashChange=!0,O.canonicalUrl=w,O.shouldScroll=P,O.hashFragment=S,O.scrollableSegments=[],(0,c.handleMutable)(t,O);let L=t.tree,B=t.cache,j=[];for(let e of g){let{pathToSegment:r,seedData:o,head:c,isHeadPartial:f,isRootRender:g}=e,E=e.tree,R=["",...r],P=(0,a.applyRouterStatePatchToTree)(R,L,E,M);if(null===P&&(P=(0,a.applyRouterStatePatchToTree)(R,C,E,M)),null!==P){if(o&&g&&T){let e=(0,m.startPPRNavigation)(A,B,L,E,o,c,f,!1,j);if(null!==e){if(null===e.route)return b(t,O,M,N);P=e.route;let r=e.node;null!==r&&(O.cache=r);let o=e.dynamicRequestTree;if(null!==o){let r=(0,n.fetchServerResponse)(v,{flightRouterState:o,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,r)}}else P=E}else{if((0,s.isNavigatingToNewRootLayout)(L,P))return b(t,O,M,N);let n=(0,d.createEmptyCacheNode)(),o=!1;for(let t of(H.status!==l.PrefetchCacheEntryStatus.stale||I?o=(0,h.applyFlightData)(A,B,n,e,H):(o=function(e,t,r,n){let o=!1;for(let i of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),_(n).map(e=>[...r,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,i),o=!0;return o}(n,B,r,E),H.lastUsedTime=A),(0,u.shouldHardNavigate)(R,L)?(n.rsc=B.rsc,n.prefetchRsc=B.prefetchRsc,(0,i.invalidateCacheBelowFlightSegmentPath)(n,B,r),O.cache=n):o&&(O.cache=n,B=n),_(E))){let e=[...r,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&j.push(e)}}L=P}}return O.patchedTree=L,O.canonicalUrl=w,O.scrollableSegments=j,O.hashFragment=S,O.shouldScroll=P,(0,c.handleMutable)(t,O)},()=>t)}}});let n=r(9008),o=r(7391),i=r(8468),a=r(6770),u=r(5951),s=r(2030),l=r(9154),c=r(9435),h=r(6928),f=r(5076),d=r(9752),p=r(3913),m=r(5956),g=r(5334),y=r(7464),E=r(9707);function b(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function _(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,o]of Object.entries(n))for(let n of _(o))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DYNAMIC_STALETIME_MS:function(){return f},STATIC_STALETIME_MS:function(){return d},createSeededPrefetchCacheEntry:function(){return l},getOrCreatePrefetchCacheEntry:function(){return s},prunePrefetchCache:function(){return h}});let n=r(9008),o=r(9154),i=r(5076);function a(e,t,r){let n=e.pathname;return(t&&(n+=e.search),r)?""+r+"%"+n:n}function u(e,t,r){return a(e,t===o.PrefetchKind.FULL,r)}function s(e){let{url:t,nextUrl:r,tree:n,prefetchCache:i,kind:u,allowAliasing:s=!0}=e,l=function(e,t,r,n,i){for(let u of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[r,null])){let r=a(e,!0,u),s=a(e,!1,u),l=e.search?r:s,c=n.get(l);if(c&&i){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let h=n.get(s);if(i&&e.search&&t!==o.PrefetchKind.FULL&&h&&!h.key.includes("%"))return{...h,aliased:!0}}if(t!==o.PrefetchKind.FULL&&i){for(let t of n.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,u,r,i,s);return l?(l.status=p(l),l.kind!==o.PrefetchKind.FULL&&u===o.PrefetchKind.FULL&&l.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:null!=u?u:o.PrefetchKind.TEMPORARY})}),u&&l.kind===o.PrefetchKind.TEMPORARY&&(l.kind=u),l):c({tree:n,url:t,nextUrl:r,prefetchCache:i,kind:u||o.PrefetchKind.TEMPORARY})}function l(e){let{nextUrl:t,tree:r,prefetchCache:n,url:i,data:a,kind:s}=e,l=a.couldBeIntercepted?u(i,s,t):u(i,s),c={treeAtTimeOfPrefetch:r,data:Promise.resolve(a),kind:s,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:-1,key:l,status:o.PrefetchCacheEntryStatus.fresh,url:i};return n.set(l,c),c}function c(e){let{url:t,kind:r,tree:a,nextUrl:s,prefetchCache:l}=e,c=u(t,r),h=i.prefetchQueue.enqueue(()=>(0,n.fetchServerResponse)(t,{flightRouterState:a,nextUrl:s,prefetchKind:r}).then(e=>{let r;if(e.couldBeIntercepted&&(r=function(e){let{url:t,nextUrl:r,prefetchCache:n,existingCacheKey:o}=e,i=n.get(o);if(!i)return;let a=u(t,i.kind,r);return n.set(a,{...i,key:a}),n.delete(o),a}({url:t,existingCacheKey:c,nextUrl:s,prefetchCache:l})),e.prerendered){let t=l.get(null!=r?r:c);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),f={treeAtTimeOfPrefetch:a,data:h,kind:r,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:o.PrefetchCacheEntryStatus.fresh,url:t};return l.set(c,f),f}function h(e){for(let[t,r]of e)p(r)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("0"),d=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n,staleTime:i}=e;return -1!==i?Date.now()<r+i?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=n?n:r)+f?n?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<r+d?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<r+d?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var o="",i=r+1;i<e.length;){var a=e.charCodeAt(i);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){o+=e[i++];continue}break}if(!o)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:o}),r=i;continue}if("("===n){var u=1,s="",i=r+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){s+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--u){i++;break}}else if("("===e[i]&&(u++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);s+=e[i++]}if(u)throw TypeError("Unbalanced pattern at "+r);if(!s)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:s}),r=i;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,a="[^"+o(t.delimiter||"/#?")+"]+?",u=[],s=0,l=0,c="",h=function(e){if(l<r.length&&r[l].type===e)return r[l++].value},f=function(e){var t=h(e);if(void 0!==t)return t;var n=r[l];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},d=function(){for(var e,t="";e=h("CHAR")||h("ESCAPED_CHAR");)t+=e;return t};l<r.length;){var p=h("CHAR"),m=h("NAME"),g=h("PATTERN");if(m||g){var y=p||"";-1===i.indexOf(y)&&(c+=y,y=""),c&&(u.push(c),c=""),u.push({name:m||s++,prefix:y,suffix:"",pattern:g||a,modifier:h("MODIFIER")||""});continue}var E=p||h("ESCAPED_CHAR");if(E){c+=E;continue}if(c&&(u.push(c),c=""),h("OPEN")){var y=d(),b=h("NAME")||"",_=h("PATTERN")||"",v=d();f("CLOSE"),u.push({name:b||(_?s++:""),pattern:b&&!_?a:_,prefix:y,suffix:v,modifier:h("MODIFIER")||""});continue}f("END")}return u}function r(e,t){void 0===t&&(t={});var r=i(t),n=t.encode,o=void 0===n?function(e){return e}:n,a=t.validate,u=void 0===a||a,s=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){r+=i;continue}var a=t?t[i.name]:void 0,l="?"===i.modifier||"*"===i.modifier,c="*"===i.modifier||"+"===i.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===a.length){if(l)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var h=0;h<a.length;h++){var f=o(a[h],i);if(u&&!s[n].test(f))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+f+'"');r+=i.prefix+f+i.suffix}continue}if("string"==typeof a||"number"==typeof a){var f=o(String(a),i);if(u&&!s[n].test(f))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+f+'"');r+=i.prefix+f+i.suffix;continue}if(!l){var d=c?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+d)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,o=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var i=n[0],a=n.index,u=Object.create(null),s=1;s<n.length;s++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?u[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return o(e,r)}):u[r.name]=o(n[e],r)}}(s);return{path:i,index:a,params:u}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,u=r.start,s=r.end,l=r.encode,c=void 0===l?function(e){return e}:l,h="["+o(r.endsWith||"")+"]|$",f="["+o(r.delimiter||"/#?")+"]",d=void 0===u||u?"^":"",p=0;p<e.length;p++){var m=e[p];if("string"==typeof m)d+=o(c(m));else{var g=o(c(m.prefix)),y=o(c(m.suffix));if(m.pattern)if(t&&t.push(m),g||y)if("+"===m.modifier||"*"===m.modifier){var E="*"===m.modifier?"?":"";d+="(?:"+g+"((?:"+m.pattern+")(?:"+y+g+"(?:"+m.pattern+"))*)"+y+")"+E}else d+="(?:"+g+"("+m.pattern+")"+y+")"+m.modifier;else d+="("+m.pattern+")"+m.modifier;else d+="(?:"+g+y+")"+m.modifier}}if(void 0===s||s)a||(d+=f+"?"),d+=r.endsWith?"(?="+h+")":"$";else{var b=e[e.length-1],_="string"==typeof b?f.indexOf(b[b.length-1])>-1:void 0===b;a||(d+="(?:"+f+"(?="+h+"))?"),_||(d+="(?="+f+"|"+h+")")}return new RegExp(d,i(r))}function u(t,r,n){if(t instanceof RegExp){if(!r)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var s=0;s<o.length;s++)r.push({name:s,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return u(e,r,n).source}).join("|")+")",i(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(u(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=u})(),e.exports=t})()},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return n.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return i},getBotType:function(){return s},isBot:function(){return u}});let n=r(5796),o=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,i=n.HTML_LIMITED_BOT_UA_RE.source;function a(e){return n.HTML_LIMITED_BOT_UA_RE.test(e)}function u(e){return o.test(e)||a(e)}function s(e){return o.test(e)?"dom":a(e)?"html":void 0}},5471:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{A:()=>n})},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return l},parseDestination:function(){return h},prepareDestination:function(){return f}});let n=r(5362),o=r(3293),i=r(6759),a=r(1437),u=r(8212);function s(e){return e.replace(/__ESC_COLON_/gi,":")}function l(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let o={},i=r=>{let n,i=r.key;switch(r.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,u.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[i];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return o[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(i)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===r.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!r.every(e=>i(e))||n.some(e=>i(e)))&&o}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function h(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,i.parseUrl)(t),n=r.pathname;n&&(n=s(n));let a=r.href;a&&(a=s(a));let u=r.hostname;u&&(u=s(u));let l=r.hash;return l&&(l=s(l)),{...r,pathname:n,hostname:u,href:a,hash:l}}function f(e){let t,r,o=Object.assign({},e.query),i=h(e),{hostname:u,query:l}=i,f=i.pathname;i.hash&&(f=""+f+i.hash);let d=[],p=[];for(let e of((0,n.pathToRegexp)(f,p),p))d.push(e.name);if(u){let e=[];for(let t of((0,n.pathToRegexp)(u,e),e))d.push(t.name)}let m=(0,n.compile)(f,{validate:!1});for(let[r,o]of(u&&(t=(0,n.compile)(u,{validate:!1})),Object.entries(l)))Array.isArray(o)?l[r]=o.map(t=>c(s(t),e.params)):"string"==typeof o&&(l[r]=c(s(o),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>d.includes(e)))for(let t of g)t in l||(l[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(f))for(let t of f.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,o]=(r=m(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=n,i.hash=(o?"#":"")+(o||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...o,...i.query},{newUrl:r,destQuery:l,parsedDestination:i}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return E}});let n=r(740),o=r(687),i=n._(r(3210)),a=r(195),u=r(2142),s=r(9154),l=r(3038),c=r(9289),h=r(6127);r(148);let f=r(3406),d=r(1794),p=r(3690);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){let t,r,n,[a,g]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),E=(0,i.useRef)(null),{href:b,as:_,children:v,prefetch:R=null,passHref:T,replace:P,shallow:A,scroll:O,onClick:S,onMouseEnter:M,onTouchStart:N,legacyBehavior:H=!1,onNavigate:C,ref:I,unstable_dynamicOnHover:w,...L}=e;t=v,H&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let B=i.default.useContext(u.AppRouterContext),j=!1!==R,U=null===R?s.PrefetchKind.AUTO:s.PrefetchKind.FULL,{href:D,as:x}=i.default.useMemo(()=>{let e=m(b);return{href:e,as:_?m(_):e}},[b,_]);H&&(r=i.default.Children.only(t));let F=H?r&&"object"==typeof r&&r.ref:I,k=i.default.useCallback(e=>(null!==B&&(E.current=(0,f.mountLinkInstance)(e,D,B,U,j,g)),()=>{E.current&&((0,f.unmountLinkForCurrentNavigation)(E.current),E.current=null),(0,f.unmountPrefetchableInstance)(e)}),[j,D,B,U,g]),G={ref:(0,l.useMergedRef)(k,F),onClick(e){H||"function"!=typeof S||S(e),H&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),B&&(e.defaultPrevented||function(e,t,r,n,o,a,u){let{nodeName:s}=e.currentTarget;if(!("A"===s.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(u){let e=!1;if(u({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==a||a,n.current)})}}(e,D,x,E,P,O,C))},onMouseEnter(e){H||"function"!=typeof M||M(e),H&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),B&&j&&(0,f.onNavigationIntent)(e.currentTarget,!0===w)},onTouchStart:function(e){H||"function"!=typeof N||N(e),H&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),B&&j&&(0,f.onNavigationIntent)(e.currentTarget,!0===w)}};return(0,c.isAbsoluteUrl)(x)?G.href=x:H&&!T&&("a"!==r.type||"href"in r.props)||(G.href=(0,h.addBasePath)(x)),n=H?i.default.cloneElement(r,G):(0,o.jsx)("a",{...L,...G,children:t}),(0,o.jsx)(y.Provider,{value:a,children:n})}r(2708);let y=(0,i.createContext)(f.IDLE_LINK_STATUS),E=()=>(0,i.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,a]=r,[u,s]=t;return(0,o.matchSegment)(u,i)?!(t.length<=2)&&e((0,n.getNextFlightSegmentPath)(t),a[s]):!!Array.isArray(u)}}});let n=r(4007),o=r(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return d},startPPRNavigation:function(){return l},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],o=t.parallelRoutes,a=new Map(o);for(let t in n){let r=n[t],u=r[0],s=(0,i.createRouterCacheKey)(u),l=o.get(t);if(void 0!==l){let n=l.get(s);if(void 0!==n){let o=e(n,r),i=new Map(l);i.set(s,o),a.set(t,i)}}}let u=t.rsc,s=y(u)&&"pending"===u.status;return{lazyData:null,rsc:u,head:t.head,prefetchHead:s?t.prefetchHead:[null,null],prefetchRsc:s?t.prefetchRsc:null,loading:t.loading,parallelRoutes:a,navigatedAt:t.navigatedAt}}}});let n=r(3913),o=r(4077),i=r(3123),a=r(2030),u=r(5334),s={route:null,node:null,dynamicRequestTree:null,children:null};function l(e,t,r,a,u,l,f,d,p){return function e(t,r,a,u,l,f,d,p,m,g,y){let E=a[1],b=u[1],_=null!==f?f[2]:null;l||!0===u[4]&&(l=!0);let v=r.parallelRoutes,R=new Map(v),T={},P=null,A=!1,O={};for(let r in b){let a,u=b[r],h=E[r],f=v.get(r),S=null!==_?_[r]:null,M=u[0],N=g.concat([r,M]),H=(0,i.createRouterCacheKey)(M),C=void 0!==h?h[0]:void 0,I=void 0!==f?f.get(H):void 0;if(null!==(a=M===n.DEFAULT_SEGMENT_KEY?void 0!==h?{route:h,node:null,dynamicRequestTree:null,children:null}:c(t,h,u,I,l,void 0!==S?S:null,d,p,N,y):m&&0===Object.keys(u[1]).length?c(t,h,u,I,l,void 0!==S?S:null,d,p,N,y):void 0!==h&&void 0!==C&&(0,o.matchSegment)(M,C)&&void 0!==I&&void 0!==h?e(t,I,h,u,l,S,d,p,m,N,y):c(t,h,u,I,l,void 0!==S?S:null,d,p,N,y))){if(null===a.route)return s;null===P&&(P=new Map),P.set(r,a);let e=a.node;if(null!==e){let t=new Map(f);t.set(H,e),R.set(r,t)}let t=a.route;T[r]=t;let n=a.dynamicRequestTree;null!==n?(A=!0,O[r]=n):O[r]=t}else T[r]=u,O[r]=u}if(null===P)return null;let S={lazyData:null,rsc:r.rsc,prefetchRsc:r.prefetchRsc,head:r.head,prefetchHead:r.prefetchHead,loading:r.loading,parallelRoutes:R,navigatedAt:t};return{route:h(u,T),node:S,dynamicRequestTree:A?h(u,O):null,children:P}}(e,t,r,a,!1,u,l,f,d,[],p)}function c(e,t,r,n,o,l,c,d,p,m){return!o&&(void 0===t||(0,a.isNavigatingToNewRootLayout)(t,r))?s:function e(t,r,n,o,a,s,l,c){let d,p,m,g,y=r[1],E=0===Object.keys(y).length;if(void 0!==n&&n.navigatedAt+u.DYNAMIC_STALETIME_MS>t)d=n.rsc,p=n.loading,m=n.head,g=n.navigatedAt;else if(null===o)return f(t,r,null,a,s,l,c);else if(d=o[1],p=o[3],m=E?a:null,g=t,o[4]||s&&E)return f(t,r,o,a,s,l,c);let b=null!==o?o[2]:null,_=new Map,v=void 0!==n?n.parallelRoutes:null,R=new Map(v),T={},P=!1;if(E)c.push(l);else for(let r in y){let n=y[r],o=null!==b?b[r]:null,u=null!==v?v.get(r):void 0,h=n[0],f=l.concat([r,h]),d=(0,i.createRouterCacheKey)(h),p=e(t,n,void 0!==u?u.get(d):void 0,o,a,s,f,c);_.set(r,p);let m=p.dynamicRequestTree;null!==m?(P=!0,T[r]=m):T[r]=n;let g=p.node;if(null!==g){let e=new Map;e.set(d,g),R.set(r,e)}}return{route:r,node:{lazyData:null,rsc:d,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:R,navigatedAt:g},dynamicRequestTree:P?h(r,T):null,children:_}}(e,r,n,l,c,d,p,m)}function h(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}function f(e,t,r,n,o,a,u){let s=h(t,t[1]);return s[3]="refetch",{route:t,node:function e(t,r,n,o,a,u,s){let l=r[1],c=null!==n?n[2]:null,h=new Map;for(let r in l){let n=l[r],f=null!==c?c[r]:null,d=n[0],p=u.concat([r,d]),m=(0,i.createRouterCacheKey)(d),g=e(t,n,void 0===f?null:f,o,a,p,s),y=new Map;y.set(m,g),h.set(r,y)}let f=0===h.size;f&&s.push(u);let d=null!==n?n[1]:null,p=null!==n?n[3]:null;return{lazyData:null,parallelRoutes:h,prefetchRsc:void 0!==d?d:null,prefetchHead:f?o:[null,null],loading:void 0!==p?p:null,rsc:E(),head:f?E():null,navigatedAt:t}}(e,t,r,n,o,a,u),dynamicRequestTree:s,children:null}}function d(e,t){t.then(t=>{let{flightData:r}=t;if("string"!=typeof r){for(let t of r){let{segmentPath:r,tree:n,seedData:a,head:u}=t;a&&function(e,t,r,n,a){let u=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],i=u.children;if(null!==i){let e=i.get(r);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(n,t)){u=e;continue}}}return}!function e(t,r,n,a){if(null===t.dynamicRequestTree)return;let u=t.children,s=t.node;if(null===u){null!==s&&(function e(t,r,n,a,u){let s=r[1],l=n[1],c=a[2],h=t.parallelRoutes;for(let t in s){let r=s[t],n=l[t],a=c[t],f=h.get(t),d=r[0],p=(0,i.createRouterCacheKey)(d),g=void 0!==f?f.get(p):void 0;void 0!==g&&(void 0!==n&&(0,o.matchSegment)(d,n[0])&&null!=a?e(g,r,n,a,u):m(r,g,null))}let f=t.rsc,d=a[1];null===f?t.rsc=d:y(f)&&f.resolve(d);let p=t.head;y(p)&&p.resolve(u)}(s,t.route,r,n,a),t.dynamicRequestTree=null);return}let l=r[1],c=n[2];for(let t in r){let r=l[t],n=c[t],i=u.get(t);if(void 0!==i){let t=i.route[0];if((0,o.matchSegment)(r[0],t)&&null!=n)return e(i,r,n,a)}}}(u,r,n,a)}(e,r,n,a,u)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)m(e.route,r,t);else for(let e of n.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,r){let n=e[1],o=t.parallelRoutes;for(let e in n){let t=n[e],a=o.get(e);if(void 0===a)continue;let u=t[0],s=(0,i.createRouterCacheKey)(u),l=a.get(s);void 0!==l&&m(t,l,r)}let a=t.rsc;y(a)&&(null===r?a.resolve(null):a.reject(r));let u=t.head;y(u)&&u.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function E(){let e,t,r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=g,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6127:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(8834),o=r(4674);function i(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6280:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return h}});let n=r(2584),o=r(9294),i=r(3033),a=r(4971),u=r(23),s=r(8388),l=r(6926),c=(r(4523),r(8719));function h(){let e=o.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,c.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return d(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new u.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=f.get(l);if(n)return n;let o=(0,s.makeHangingPromise)(l.renderSignal,"`headers()`");return f.set(l,o),Object.defineProperties(o,{append:{value:function(){let e=`\`headers().append(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${p(arguments[0])})\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${p(arguments[0])}, ...)\``,t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=g(r,e);(0,a.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,l)}}}),o}else"prerender-ppr"===t.type?(0,a.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,a.throwToInterruptStaticGeneration)("headers",e,t);(0,a.trackDynamicDataInDynamicRender)(e,t)}return d((0,i.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function d(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function p(e){return"string"==typeof e?`'${e}'`:"..."}let m=(0,l.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},6294:(e,t,r)=>{"use strict";let n=r(3033),o=r(9294),i=r(4971),a=r(6926),u=r(23),s=r(8479);function l(){let e=o.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return c(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return c(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function c(e,t){let r,n=h.get(l);return n||(r=f(e),h.set(e,r),r)}let h=new WeakMap;function f(e){let t=new d(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class d{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){m("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){m("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let p=(0,a.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function m(e){let t=o.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new u.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new s.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},6312:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n})},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return g},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},normalizeVercelUrl:function(){return d}});let n=r(9551),o=r(1959),i=r(2437),a=r(4396),u=r(8034),s=r(5526),l=r(2887),c=r(4722),h=r(6143),f=r(7912);function d(e,t,r){let o=(0,n.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let n=e!==h.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(h.NEXT_QUERY_PARAM_PREFIX),i=e!==h.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(h.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete o.query[e]}e.url=(0,n.format)(o)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let o,{optional:i,repeat:a}=r.groups[n],u=`[${a?"...":""}${n}]`;i&&(u=`[${u}]`);let s=t[n];o=Array.isArray(s)?s.map(e=>e&&encodeURIComponent(e)).join("/"):s?encodeURIComponent(s):"",e=e.replaceAll(u,o)}return e}function m(e,t,r,n){let o={};for(let i of Object.keys(t.groups)){let a=e[i];"string"==typeof a?a=(0,c.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(c.normalizeRscURL));let u=r[i],s=t.groups[i].optional;if((Array.isArray(u)?u.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(u))||void 0===a&&!(s&&n))return{params:{},hasValidParams:!1};s&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${i}]]`))&&(a=void 0,delete e[i]),a&&"string"==typeof a&&t.groups[i].repeat&&(a=a.split("/")),a&&(o[i]=a)}return{params:o,hasValidParams:!0}}function g({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:h,caseSensitive:g}){let y,E,b;return c&&(y=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(E=(0,u.getRouteMatcher)(y))(e)),{handleRewrites:function(a,u){let f={},d=u.pathname,p=n=>{let l=(0,i.getPathMatch)(n.source+(h?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!u.pathname)return!1;let p=l(u.pathname);if((n.has||n.missing)&&p){let e=(0,s.matchHas)(a,u.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:i,destQuery:a}=(0,s.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:u.query});if(i.protocol)return!0;if(Object.assign(f,a,p),Object.assign(u.query,i.query),delete i.query,Object.assign(u,i),!(d=u.pathname))return!1;if(r&&(d=d.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(d,t.locales);d=e.pathname,u.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(d===e)return!0;if(c&&E){let e=E(d);if(e)return u.query={...u.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(d!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,l.removeTrailingSlash)(d||"");return t===(0,l.removeTrailingSlash)(e)||(null==E?void 0:E(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return f},defaultRouteRegex:y,dynamicRouteMatcher:E,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:r}=y,n=(0,u.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,f.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let o={};for(let e of Object.keys(r)){let i=r[e];if(!i)continue;let a=t[i],u=n[e];if(!a.optional&&!u)return null;o[a.pos]=u}return o}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>y&&b?m(e,y,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>d(e,t,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}function y(e,t){return"string"==typeof e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[h.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[h.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let n=r(6127);function o(e,t){if(e.startsWith(".")){let r=t.origin+t.pathname;return new URL((r.endsWith("/")?r:r+"/")+e)}return new URL((0,n.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},i=t.split(n),a=(r||{}).decode||e,u=0;u<i.length;u++){var s=i[u],l=s.indexOf("=");if(!(l<0)){var c=s.substr(0,l).trim(),h=s.substr(++l,s.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==o[c]&&(o[c]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return o},t.serialize=function(e,t,n){var i=n||{},a=i.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var u=a(t);if(u&&!o.test(u))throw TypeError("argument val is invalid");var s=e+"="+u;if(null!=i.maxAge){var l=i.maxAge-0;if(isNaN(l)||!isFinite(l))throw TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(l)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");s+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");s+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");s+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(s+="; HttpOnly"),i.secure&&(s+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"none":s+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return s};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6493:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let n=r(5232);function o(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},6736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(2255);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return i}});let n=r(2785),o=r(3736);function i(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6770:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,s){let l,[c,h,f,d,p]=r;if(1===t.length){let e=u(r,n);return(0,a.addRefreshMarkerToActiveParallelSegments)(e,s),e}let[m,g]=t;if(!(0,i.matchSegment)(m,c))return null;if(2===t.length)l=u(h[g],n);else if(null===(l=e((0,o.getNextFlightSegmentPath)(t),h[g],n,s)))return null;let y=[t[0],{...h,[g]:l},f,d];return p&&(y[4]=!0),(0,a.addRefreshMarkerToActiveParallelSegments)(y,s),y}}});let n=r(3913),o=r(4007),i=r(4077),a=r(2308);function u(e,t){let[r,o]=e,[a,s]=t;if(a===n.DEFAULT_SEGMENT_KEY&&r!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(r,a)){let t={};for(let e in o)void 0!==s[e]?t[e]=u(o[e],s[e]):t[e]=o[e];for(let e in s)t[e]||(t[e]=s[e]);let n=[r,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return h},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return l},permanentRedirect:function(){return s},redirect:function(){return u}});let n=r(2836),o=r(9026),i=r(9121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function u(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function s(e,t){throw void 0===t&&(t=o.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function l(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function h(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return i}});let n=r(1500),o=r(3898);function i(e,t,r,i,a){let{tree:u,seedData:s,head:l,isRootRender:c}=i;if(null===s)return!1;if(c){let o=s[1];r.loading=s[3],r.rsc=o,r.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(e,r,t,u,s,l,a)}else r.rsc=t.rsc,r.prefetchRsc=t.prefetchRsc,r.parallelRoutes=new Map(t.parallelRoutes),r.loading=t.loading,(0,o.fillCacheWithNewSubTreeData)(e,r,t,i,a);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(3210),o=r(1215),i="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(i)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(i);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(i)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[u,s]=(0,n.useState)(""),l=(0,n.useRef)(void 0);return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==l.current&&l.current!==e&&s(e),l.current=e},[t]),r?(0,o.createPortal)(u,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let a=i.length<=2,[u,s]=i,l=(0,o.createRouterCacheKey)(s),c=r.parallelRoutes.get(u),h=t.parallelRoutes.get(u);h&&h!==c||(h=new Map(c),t.parallelRoutes.set(u,h));let f=null==c?void 0:c.get(l),d=h.get(l);if(a){d&&d.lazyData&&d!==f||h.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!d||!f){d||h.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return d===f&&(d={lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes),loading:d.loading},h.set(l,d)),e(d,f,(0,n.getNextFlightSegmentPath)(i))}}});let n=r(4007),o=r(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return o.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return u.unauthorized},unstable_rethrow:function(){return s.unstable_rethrow}});let n=r(6897),o=r(9026),i=r(2765),a=r(8976),u=r(899),s=r(163);class l extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new l}delete(){throw new l}set(){throw new l}sort(){throw new l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return N}});let n=r(1264),o=r(1448),i=r(1563),a=r(9154),u=r(6361),s=r(7391),l=r(5232),c=r(6770),h=r(2030),f=r(9435),d=r(1500),p=r(9752),m=r(8214),g=r(6493),y=r(2308),E=r(4007),b=r(6875),_=r(7860),v=r(5334),R=r(5942),T=r(6736),P=r(4642);r(593);let{createFromFetch:A,createTemporaryReferenceSet:O,encodeReply:S}=r(9357);async function M(e,t,r){let a,s,{actionId:l,actionArgs:c}=r,h=O(),f=(0,P.extractInfoFromServerReferenceId)(l),d="use-cache"===f.type?(0,P.omitUnusedArgs)(c,f):c,p=await S(d,{temporaryReferences:h}),m=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION_HEADER]:l,[i.NEXT_ROUTER_STATE_TREE_HEADER]:encodeURIComponent(JSON.stringify(e.tree)),...{},...t?{[i.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":a=_.RedirectType.push;break;case"replace":a=_.RedirectType.replace;break;default:a=void 0}let v=!!m.headers.get(i.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");s={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){s={paths:[],tag:!1,cookie:!1}}let R=y?(0,u.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,T=m.headers.get("content-type");if(null==T?void 0:T.startsWith(i.RSC_CONTENT_TYPE_HEADER)){let e=await A(Promise.resolve(m),{callServer:n.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:h});return y?{actionFlightData:(0,E.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:s,isPrerender:v}:{actionResult:e.a,actionFlightData:(0,E.normalizeFlightData)(e.f),redirectLocation:R,redirectType:a,revalidatedParts:s,isPrerender:v}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===T?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:R,redirectType:a,revalidatedParts:s,isPrerender:v}}function N(e,t){let{resolve:r,reject:n}=t,o={},i=e.tree;o.preserveCustomHistoryState=!1;let u=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,E=Date.now();return M(e,u,t).then(async m=>{let P,{actionResult:A,actionFlightData:O,redirectLocation:S,redirectType:M,isPrerender:N,revalidatedParts:H}=m;if(S&&(M===_.RedirectType.replace?(e.pushRef.pendingPush=!1,o.pendingPush=!1):(e.pushRef.pendingPush=!0,o.pendingPush=!0),o.canonicalUrl=P=(0,s.createHrefFromUrl)(S,!1)),!O)return(r(A),S)?(0,l.handleExternalUrl)(e,o,S.href,e.pushRef.pendingPush):e;if("string"==typeof O)return r(A),(0,l.handleExternalUrl)(e,o,O,e.pushRef.pendingPush);let C=H.paths.length>0||H.tag||H.cookie;for(let n of O){let{tree:a,seedData:s,head:f,isRootRender:m}=n;if(!m)return console.log("SERVER ACTION APPLY FAILED"),r(A),e;let b=(0,c.applyRouterStatePatchToTree)([""],i,a,P||e.canonicalUrl);if(null===b)return r(A),(0,g.handleSegmentMismatch)(e,t,a);if((0,h.isNavigatingToNewRootLayout)(i,b))return r(A),(0,l.handleExternalUrl)(e,o,P||e.canonicalUrl,e.pushRef.pendingPush);if(null!==s){let t=s[1],r=(0,p.createEmptyCacheNode)();r.rsc=t,r.prefetchRsc=null,r.loading=s[3],(0,d.fillLazyItemsTillLeafWithHead)(E,r,void 0,a,s,f,void 0),o.cache=r,o.prefetchCache=new Map,C&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:E,state:e,updatedTree:b,updatedCache:r,includeNextUrl:!!u,canonicalUrl:o.canonicalUrl||e.canonicalUrl})}o.patchedTree=b,i=b}return S&&P?(C||((0,v.createSeededPrefetchCacheEntry)({url:S,data:{flightData:O,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:N?a.PrefetchKind.FULL:a.PrefetchKind.AUTO}),o.prefetchCache=e.prefetchCache),n((0,b.getRedirectError)((0,T.hasBasePath)(P)?(0,R.removeBasePath)(P):P,M||_.RedirectType.push))):r(A),(0,f.handleMutable)(e,o)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9008),r(7391),r(6770),r(2030),r(5232),r(9435),r(6928),r(9752),r(6493),r(8214);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(4827);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=o[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>i(e)):a[e]=i(r))}return a}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return u},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return s},isMetadataPage:function(){return h},isMetadataRoute:function(){return f},isMetadataRouteFile:function(){return l},isStaticMetadataRoute:function(){return c}});let n=r(2958),o=r(4722),i=r(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},u=["js","jsx","ts","tsx"],s=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function l(e,t,r){let o=(r?"":"?")+"$",i=`\\d?${r?"":"(-\\w{6})?"}`,u=[RegExp(`^[\\\\/]robots${s(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${s(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${s(["xml"],t)}${o}`),RegExp(`[\\\\/]${a.icon.filename}${i}${s(a.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${a.apple.filename}${i}${s(a.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${a.openGraph.filename}${i}${s(a.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${a.twitter.filename}${i}${s(a.twitter.extensions,t)}${o}`)],l=(0,n.normalizePathSep)(e);return u.some(e=>e.test(l))}function c(e){let t=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&l(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function h(e){return!(0,i.isAppRouteRoute)(e)&&l(e,[],!1)}function f(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,i.isAppRouteRoute)(e)&&l(t,[],!1)}},8468:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let a=i.length<=2,[u,s]=i,l=(0,n.createRouterCacheKey)(s),c=r.parallelRoutes.get(u);if(!c)return;let h=t.parallelRoutes.get(u);if(h&&h!==c||(h=new Map(c),t.parallelRoutes.set(u,h)),a)return void h.delete(l);let f=c.get(l),d=h.get(l);d&&f&&(d===f&&(d={lazyData:d.lazyData,rsc:d.rsc,prefetchRsc:d.prefetchRsc,head:d.head,prefetchHead:d.prefetchHead,parallelRoutes:new Map(d.parallelRoutes)},h.set(l,d)),e(d,f,(0,o.getNextFlightSegmentPath)(i)))}}});let n=r(3123),o=r(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8521:(e,t,r)=>{"use strict";r.d(t,{Dk:()=>eH,kc:()=>ej,Ym:()=>eB,c3:()=>eL});var n,o,i,a,u,s,l,c=r(3210),h=function(e,t){return(h=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function f(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}h(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var d=function(){return(d=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};Object.create;function p(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function m(e,t){var r=t&&t.cache?t.cache:_,n=t&&t.serializer?t.serializer:E;return(t&&t.strategy?t.strategy:function(e,t){var r,n,o=1===e.length?g:y;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function g(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function y(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}Object.create,"function"==typeof SuppressedError&&SuppressedError;var E=function(){return JSON.stringify(arguments)},b=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),_={create:function(){return new b}},v={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,y.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,g.bind(this,e,r,n)}};function R(e){return e.type===o.literal}function T(e){return e.type===o.number}function P(e){return e.type===o.date}function A(e){return e.type===o.time}function O(e){return e.type===o.select}function S(e){return e.type===o.plural}function M(e){return e.type===o.tag}function N(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function H(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var C=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,I=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,w=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,L=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,B=/^(@+)?(\+|#+)?[rs]?$/g,j=/(\*)(0+)|(#+)(0+)|(0+)/g,U=/^(0+)$/;function D(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(B,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function x(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function F(e){var t=x(e);return t||{}}var k={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},G=new RegExp("^".concat(C.source,"*")),V=new RegExp("".concat(C.source,"*$"));function K(e,t){return{start:e,end:t}}var X=!!String.prototype.startsWith&&"_a".startsWith("a",1),z=!!String.fromCodePoint,$=!!Object.fromEntries,W=!!String.prototype.codePointAt,q=!!String.prototype.trimStart,Y=!!String.prototype.trimEnd,Z=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},Q=!0;try{Q=(null==(a=ei("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){Q=!1}var J=X?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},ee=z?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},et=$?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},er=W?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},en=q?function(e){return e.trimStart()}:function(e){return e.replace(G,"")},eo=Y?function(e){return e.trimEnd()}:function(e){return e.replace(V,"")};function ei(e,t){return new RegExp(e,t)}if(Q){var ea=ei("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");u=function(e,t){var r;return ea.lastIndex=t,null!=(r=ea.exec(e)[1])?r:""}}else u=function(e,t){for(var r=[];;){var n,o=er(e,t);if(void 0===o||el(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return ee.apply(void 0,r)};var eu=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var u=this.parseArgument(e,r);if(u.err)return u;i.push(u.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var s=this.clonePosition();this.bump(),i.push({type:o.pound,location:K(s,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&es(this.peek()||0)){var u=this.parseTag(e,t);if(u.err)return u;i.push(u.val)}else{var u=this.parseLiteral(e,t);if(u.err)return u;i.push(u.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,K(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:K(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,K(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var u=a.val,s=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,K(r,this.clonePosition()));if(this.isEOF()||!es(this.char()))return this.error(n.INVALID_TAG,K(s,this.clonePosition()));var l=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,K(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:u,location:K(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,K(s,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var u=this.tryParseLeftAngleBracket();if(u){n+=u;continue}break}var s=K(r,this.clonePosition());return{val:{type:o.literal,value:n,location:s},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(es(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return ee.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),ee(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,K(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,K(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,K(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,K(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:K(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,K(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,K(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=u(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:K(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var u,s=this.clonePosition(),l=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(l){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,K(s,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var f=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var m=eo(p.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,K(this.clonePosition(),this.clonePosition()));h={style:m,styleLocation:K(f,this.clonePosition())}}var g=this.tryParseArgumentClose(a);if(g.err)return g;var y=K(a,this.clonePosition());if(h&&J(null==h?void 0:h.style,"::",0)){var E=en(h.style.slice(2));if("number"===l){var p=this.parseNumberSkeletonFromString(E,h.styleLocation);if(p.err)return p;return{val:{type:o.number,value:r,location:y,style:p.val},err:null}}if(0===E.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,y);var b,_=E;this.locale&&(_=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),u=i<2?1:3+(i>>1),s=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(k[t||""]||k[n||""]||k["".concat(n,"-001")]||k["001"])[0]}(t);for(("H"==s||"k"==s)&&(u=0);u-- >0;)r+="a";for(;a-- >0;)r=s+r}else"J"===o?r+="H":r+=o}return r}(E,this.locale));var m={type:i.dateTime,pattern:_,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(b={},_.replace(I,function(e){var t=e.length;switch(e[0]){case"G":b.era=4===t?"long":5===t?"narrow":"short";break;case"y":b.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":b.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":b.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":b.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"a":b.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":b.hourCycle="h12",b.hour=["numeric","2-digit"][t-1];break;case"H":b.hourCycle="h23",b.hour=["numeric","2-digit"][t-1];break;case"K":b.hourCycle="h11",b.hour=["numeric","2-digit"][t-1];break;case"k":b.hourCycle="h24",b.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":b.minute=["numeric","2-digit"][t-1];break;case"s":b.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":b.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),b):{}};return{val:{type:"date"===l?o.date:o.time,value:r,location:y,style:m},err:null}}return{val:{type:"number"===l?o.number:"date"===l?o.date:o.time,value:r,location:y,style:null!=(u=null==h?void 0:h.style)?u:null},err:null};case"plural":case"selectordinal":case"select":var v=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,K(v,d({},v)));this.bumpSpace();var R=this.parseIdentifierIfPossible(),T=0;if("select"!==l&&"offset"===R.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,K(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),R=this.parseIdentifierIfPossible(),T=p.val}var P=this.tryParsePluralOrSelectOptions(e,l,t,R);if(P.err)return P;var g=this.tryParseArgumentClose(a);if(g.err)return g;var A=K(a,this.clonePosition());if("select"===l)return{val:{type:o.select,value:r,options:et(P.val),location:A},err:null};return{val:{type:o.plural,value:r,options:et(P.val),offset:T,pluralType:"plural"===l?"cardinal":"ordinal",location:A},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,K(s,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,K(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,K(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(w).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),u=0;u<a.length;u++)if(0===a[u].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=d(d(d({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return d(d({},e),F(t))},{}));continue;case"engineering":t=d(d(d({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return d(d({},e),F(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(j,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(U.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(L.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(L,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=d(d({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=d(d({},t),D(o)));continue}if(B.test(n.stem)){t=d(d({},t),D(n.stem));continue}var i=x(n.stem);i&&(t=d(d({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!U.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=d(d({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,u=[],s=new Set,l=o.value,c=o.location;;){if(0===l.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=K(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(s.has(l))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===l&&(a=!0),this.bumpSpace();var d=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,K(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var m=this.tryParseArgumentClose(d);if(m.err)return m;u.push([l,{value:p.val,location:K(d,this.clonePosition())}]),s.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===u.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,K(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,K(this.clonePosition(),this.clonePosition())):{val:u,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var u=K(n,this.clonePosition());return o?Z(i*=r)?{val:i,err:null}:this.error(t,u):this.error(e,u)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=er(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(J(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&el(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function es(e){return e>=97&&e<=122||e>=65&&e<=90}function el(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ec(e,t){void 0===t&&(t={});var r=new eu(e,t=d({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,O(t)||S(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else T(t)&&N(t.style)||(P(t)||A(t))&&H(t.style)?delete t.style.location:M(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(s||(s={}));var eh=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return f(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ef=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),s.INVALID_VALUE,o)||this}return f(t,e),t}(eh),ed=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),s.INVALID_VALUE,n)||this}return f(t,e),t}(eh),ep=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),s.MISSING_VALUE,r)||this}return f(t,e),t}(eh);function em(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(l||(l={}));var eg=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var a,u,c=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=c.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===l.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return function e(t,r,n,i,a,u,c){if(1===t.length&&R(t[0]))return[{type:l.literal,value:t[0].value}];for(var h=[],f=0;f<t.length;f++){var d=t[f];if(R(d)){h.push({type:l.literal,value:d.value});continue}if(d.type===o.pound){"number"==typeof u&&h.push({type:l.literal,value:n.getNumberFormat(r).format(u)});continue}var p=d.value;if(!(a&&p in a))throw new ep(p,c);var m=a[p];if(d.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),h.push({type:"string"==typeof m?l.literal:l.object,value:m});continue}if(P(d)){var g="string"==typeof d.style?i.date[d.style]:H(d.style)?d.style.parsedOptions:void 0;h.push({type:l.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(A(d)){var g="string"==typeof d.style?i.time[d.style]:H(d.style)?d.style.parsedOptions:i.time.medium;h.push({type:l.literal,value:n.getDateTimeFormat(r,g).format(m)});continue}if(T(d)){var g="string"==typeof d.style?i.number[d.style]:N(d.style)?d.style.parsedOptions:void 0;g&&g.scale&&(m*=g.scale||1),h.push({type:l.literal,value:n.getNumberFormat(r,g).format(m)});continue}if(M(d)){var y=d.children,E=d.value,b=a[E];if("function"!=typeof b)throw new ed(E,"function",c);var _=b(e(y,r,n,i,a,u).map(function(e){return e.value}));Array.isArray(_)||(_=[_]),h.push.apply(h,_.map(function(e){return{type:"string"==typeof e?l.literal:l.object,value:e}}))}if(O(d)){var v=d.options[m]||d.options.other;if(!v)throw new ef(d.value,m,Object.keys(d.options),c);h.push.apply(h,e(v.value,r,n,i,a));continue}if(S(d)){var v=d.options["=".concat(m)];if(!v){if(!Intl.PluralRules)throw new eh('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',s.MISSING_INTL_API,c);var C=n.getPluralRules(r,{type:d.pluralType}).select(m-(d.offset||0));v=d.options[C]||d.options.other}if(!v)throw new ef(d.value,m,Object.keys(d.options),c);h.push.apply(h,e(v.value,r,n,i,a,m-(d.offset||0)));continue}}return h.length<2?h:h.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===l.literal&&t.type===l.literal?r.value+=t.value:e.push(t),e},[])}(c.ast,c.locales,c.formatters,c.formats,e,void 0,c.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=c.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(c.locales)[0]}},this.getAst=function(){return c.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=i||{},f=(h.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(h,["formatters"]));this.ast=e.__parse(t,d(d({},f),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(a=e.formats,n?Object.keys(a).reduce(function(e,t){var r,o;return e[t]=(r=a[t],(o=n[t])?d(d(d({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=d(d({},r[t]),o[t]||{}),e},{})):r),e},d({},a)):a),this.formatters=i&&i.formatters||(void 0===(u=this.formatterCache)&&(u={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,p([void 0],t,!1)))},{cache:em(u.number),strategy:v.variadic}),getDateTimeFormat:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,p([void 0],t,!1)))},{cache:em(u.dateTime),strategy:v.variadic}),getPluralRules:m(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,p([void 0],t,!1)))},{cache:em(u.pluralRules),strategy:v.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ec,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();class ey extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),this.code=e,t&&(this.originalMessage=t)}}var eE=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}(eE||{});function eb(...e){return e.filter(Boolean).join(".")}function e_(e){return eb(e.namespace,e.key)}function ev(e){console.error(e)}function eR(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}}function eT(e,t){return m(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:v.variadic})}function eP(e,t){return eT((...t)=>new e(...t),t)}function eA(e){return{getDateTimeFormat:eP(Intl.DateTimeFormat,e.dateTime),getNumberFormat:eP(Intl.NumberFormat,e.number),getPluralRules:eP(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:eP(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:eP(Intl.ListFormat,e.list),getDisplayNames:eP(Intl.DisplayNames,e.displayNames)}}function eO(e,t,r,n){let o=eb(n,r);if(!t)throw Error(o);let i=t;return r.split(".").forEach(t=>{let r=i[t];if(null==t||null==r)throw Error(o+` (${e})`);i=r}),i}let eS={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:2628e3,months:2628e3,quarter:7884e3,quarters:7884e3,year:31536e3,years:31536e3};var eM=r(687);let eN=(0,c.createContext)(void 0);function eH({children:e,formats:t,getMessageFallback:r,locale:n,messages:o,now:i,onError:a,timeZone:u}){let s=(0,c.useContext)(eN),l=(0,c.useMemo)(()=>s?.cache||eR(),[n,s?.cache]),h=(0,c.useMemo)(()=>s?.formatters||eA(l),[l,s?.formatters]),f=(0,c.useMemo)(()=>({...function({formats:e,getMessageFallback:t,messages:r,onError:n,...o}){return{...o,formats:e||void 0,messages:r||void 0,onError:n||ev,getMessageFallback:t||e_}}({locale:n,formats:void 0===t?s?.formats:t,getMessageFallback:r||s?.getMessageFallback,messages:void 0===o?s?.messages:o,now:i||s?.now,onError:a||s?.onError,timeZone:u||s?.timeZone}),formatters:h,cache:l}),[l,t,h,r,n,o,i,a,s,u]);return(0,eM.jsx)(eN.Provider,{value:f,children:e})}function eC(){let e=(0,c.useContext)(eN);if(!e)throw Error(void 0);return e}let eI=!1,ew="undefined"==typeof window;function eL(e){return function(e,t,r){let{cache:n,formats:o,formatters:i,getMessageFallback:a,locale:u,onError:s,timeZone:l}=eC(),h=e["!"],f="!"===t?void 0:t.slice((r+".").length);return l||eI||!ew||(eI=!0,s(new ey(eE.ENVIRONMENT_FALLBACK,void 0))),(0,c.useMemo)(()=>(function(e){let t=function(e,t,r,n=ev){try{if(!t)throw Error(void 0);let n=r?eO(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new ey(eE.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function({cache:e,formats:t,formatters:r,getMessageFallback:n=e_,locale:o,messagesOrError:i,namespace:a,onError:u,timeZone:s}){let l=i instanceof ey;function h(e,t,r){let o=new ey(t,r);return u(o),n({error:o,key:e,namespace:a})}function f(u,f,d){var p;let m,g;if(l)return n({error:i,key:u,namespace:a});try{m=eO(o,i,u,a)}catch(e){return h(u,eE.MISSING_MESSAGE,e.message)}if("object"==typeof m){let e;return h(u,Array.isArray(m)?eE.INVALID_MESSAGE:eE.INSUFFICIENT_PATH,e)}let y=(p=m,f?void 0:p);if(y)return y;r.getMessageFormat||(r.getMessageFormat=eT((...e)=>new eg(e[0],e[1],e[2],{formatters:r,...e[3]}),e.message));try{g=r.getMessageFormat(m,o,function(e,t,r){let n=eg.formats.date,o=eg.formats.time,i={...e?.dateTime,...t?.dateTime},a={date:{...n,...i},time:{...o,...i},number:{...e?.number,...t?.number}};return r&&["date","time"].forEach(e=>{let t=a[e];for(let[e,n]of Object.entries(t))t[e]={timeZone:r,...n}}),a}(t,d,s),{formatters:{...r,getDateTimeFormat:(e,t)=>r.getDateTimeFormat(e,{timeZone:s,...t})}})}catch(e){return h(u,eE.INVALID_MESSAGE,e.message)}try{let e=g.format(f?function(e){let t={};return Object.keys(e).forEach(r=>{let n,o=0,i=e[r];n="function"==typeof i?e=>{let t=i(e);return(0,c.isValidElement)(t)?(0,c.cloneElement)(t,{key:r+o++}):t}:i,t[r]=n}),t}(f):f);if(null==e)throw Error(void 0);return(0,c.isValidElement)(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(e){return h(u,eE.FORMATTING_ERROR,e.message)}}function d(e,t,r){let n=f(e,t,r);return"string"!=typeof n?h(e,eE.INVALID_MESSAGE,void 0):n}return d.rich=f,d.markup=(e,t,r)=>f(e,t,r),d.raw=e=>{if(l)return n({error:i,key:e,namespace:a});try{return eO(o,i,e,a)}catch(t){return h(e,eE.MISSING_MESSAGE,t.message)}},d.has=e=>{if(l)return!1;try{return eO(o,i,e,a),!0}catch{return!1}},d}({...e,messagesOrError:t})})({cache:n,formatters:i,getMessageFallback:a,messages:h,namespace:f,onError:s,formats:o,locale:u,timeZone:l}),[n,i,a,h,f,s,o,u,l])}({"!":eC().messages},e?`!.${e}`:"!","!")}function eB(){return eC().locale}function ej(){let{formats:e,formatters:t,locale:r,now:n,onError:o,timeZone:i}=eC();return(0,c.useMemo)(()=>(function(e){let{_cache:t=eR(),_formatters:r=eA(t),formats:n,locale:o,onError:i=ev,timeZone:a}=e;function u(e){return e?.timeZone||(a?e={...e,timeZone:a}:i(new ey(eE.ENVIRONMENT_FALLBACK,void 0))),e}function s(e,t,r,n,o){let a;try{a=function(e,t,r){let n;if("string"==typeof t){if(!(n=e?.[t])){let e=new ey(eE.MISSING_FORMAT,void 0);throw i(e),e}}else n=t;return r&&(n={...n,...r}),n}(r,e,t)}catch{return o()}try{return n(a)}catch(e){return i(new ey(eE.FORMATTING_ERROR,e.message)),o()}}function l(e,t,i){return s(t,i,n?.dateTime,t=>(t=u(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function c(){return e.now?e.now:(i(new ey(eE.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:l,number:function(e,t,i){return s(t,i,n?.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,u={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):c(),a=t.unit,u.style=t.style,u.numberingSystem=t.numberingSystem),i||(i=c());let s=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<2628e3?"week":t<31536e3?"month":"year"}(s)),u.numeric="second"===a?"auto":"always";let l=(n=a,Math.round(s/eS[n]));return r.getRelativeTimeFormat(o,u).format(l,a)}catch(t){return i(new ey(eE.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t,i){let a=[],u=new Map,l=0;for(let t of e){let e;"object"==typeof t?(e=String(l),u.set(e,t)):e=String(t),a.push(e),l++}return s(t,i,n?.list,e=>{let t=r.getListFormat(o,e).formatToParts(a).map(e=>"literal"===e.type?e.value:u.get(e.value)||e.value);return u.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i,a){return s(i,a,n?.dateTime,n=>(n=u(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[l(e),l(t)].join(" – "))}}})({formats:e,locale:r,now:n,onError:o,timeZone:i,_formatters:t}),[e,t,n,r,o,i])}},8627:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return i}});let n=r(7391),o=r(642);function i(e,t){var r;let{url:i,tree:a}=t,u=(0,n.createHrefFromUrl)(i),s=a||e.tree,l=e.cache;return{canonicalUrl:u,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:l,prefetchCache:e.prefetchCache,tree:s,nextUrl:null!=(r=(0,o.extractPathFromFlightRouterState)(s))?r:i.pathname}}r(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8830:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(9154),r(5232),r(9651),r(8627),r(8866),r(5076),r(7936),r(7810);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(1550);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:i}=(0,n.parsePath)(e);return""+t+r+o+i}},8866:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9008),o=r(7391),i=r(6770),a=r(2030),u=r(5232),s=r(9435),l=r(1500),c=r(9752),h=r(6493),f=r(8214),d=r(2308);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),E=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,n.fetchServerResponse)(new URL(m,r),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:E?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async r=>{let{flightData:n,canonicalUrl:c}=r;if("string"==typeof n)return(0,u.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(y.lazyData=null,n)){let{tree:n,seedData:s,head:f,isRootRender:_}=r;if(!_)return console.log("REFRESH FAILED"),e;let v=(0,i.applyRouterStatePatchToTree)([""],g,n,e.canonicalUrl);if(null===v)return(0,h.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(g,v))return(0,u.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let R=c?(0,o.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=R),null!==s){let e=s[1],t=s[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,l.fillLazyItemsTillLeafWithHead)(b,y,void 0,n,s,f,void 0),p.prefetchCache=new Map}await (0,d.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:v,updatedCache:y,includeNextUrl:E,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=v,g=v}return(0,s.handleMutable)(e,p)},()=>e)}r(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(1120),o=r(9769);let i=(0,n.cache)(async function(e){return(await (0,o.A)(e)).now}),a=(0,n.cache)(async function(){return(await (0,o.A)()).formats});var u=r(994),s=r(7413);let l=(0,n.cache)(async function(e){return(await (0,o.A)(e)).timeZone});async function c(e){return l(e?.locale)}var h=r(3930);let f=(0,n.cache)(async function(){return(await (0,o.A)()).locale});async function d({formats:e,locale:t,messages:r,now:n,timeZone:o,...l}){return(0,s.jsx)(u.default,{formats:void 0===e?await a():e,locale:t??await f(),messages:void 0===r?await (0,h.A)():r,now:n??await i(),timeZone:o??await c(),...l})}},8976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return E},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return f},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return s},getLocationOrigin:function(){return a},getURL:function(){return u},isAbsoluteUrl:function(){return i},isResSent:function(){return l},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function u(){let{href:e}=window.location,t=a();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let f="undefined"!=typeof performance,d=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class E extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},9435:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return i}});let n=r(642);function o(e){return void 0!==e}function i(e,t){var r,i;let a=null==(r=t.shouldScroll)||r,u=e.nextUrl;if(o(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?u=r:u||(u=e.canonicalUrl)}return{canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!a&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:a?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:a?null!=(i=null==t?void 0:t.scrollableSegments)?i:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9651:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let n=r(7391),o=r(6770),i=r(2030),a=r(5232),u=r(6928),s=r(9435),l=r(9752);function c(e,t){let{serverResponse:{flightData:r,canonicalUrl:c},navigatedAt:h}=t,f={};if(f.preserveCustomHistoryState=!1,"string"==typeof r)return(0,a.handleExternalUrl)(e,f,r,e.pushRef.pendingPush);let d=e.tree,p=e.cache;for(let t of r){let{segmentPath:r,tree:s}=t,m=(0,o.applyRouterStatePatchToTree)(["",...r],d,s,e.canonicalUrl);if(null===m)return e;if((0,i.isNavigatingToNewRootLayout)(d,m))return(0,a.handleExternalUrl)(e,f,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,n.createHrefFromUrl)(c):void 0;g&&(f.canonicalUrl=g);let y=(0,l.createEmptyCacheNode)();(0,u.applyFlightData)(h,p,y,t),f.patchedTree=m,f.cache=y,p=y,d=m}return(0,s.handleMutable)(e,f)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>o});var n=0;function o(e){return"__private_"+n+++"_"+e}},9707:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addSearchParamsToPageSegments:function(){return h},handleAliasedPrefetchEntry:function(){return c}});let n=r(3913),o=r(9752),i=r(6770),a=r(7391),u=r(3123),s=r(3898),l=r(9435);function c(e,t,r,c,f){let d,p=t.tree,m=t.cache,g=(0,a.createHrefFromUrl)(c);if("string"==typeof r)return!1;for(let t of r){if(!function e(t){if(!t)return!1;let r=t[2];if(t[3])return!0;for(let t in r)if(e(r[t]))return!0;return!1}(t.seedData))continue;let r=t.tree;r=h(r,Object.fromEntries(c.searchParams));let{seedData:a,isRootRender:l,pathToSegment:f}=t,y=["",...f];r=h(r,Object.fromEntries(c.searchParams));let E=(0,i.applyRouterStatePatchToTree)(y,p,r,g),b=(0,o.createEmptyCacheNode)();if(l&&a){let t=a[1];b.loading=a[3],b.rsc=t,function e(t,r,o,i,a){if(0!==Object.keys(i[1]).length)for(let s in i[1]){let l,c=i[1][s],h=c[0],f=(0,u.createRouterCacheKey)(h),d=null!==a&&void 0!==a[2][s]?a[2][s]:null;if(null!==d){let e=d[1],r=d[3];l={lazyData:null,rsc:h.includes(n.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:r,navigatedAt:t}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=r.parallelRoutes.get(s);p?p.set(f,l):r.parallelRoutes.set(s,new Map([[f,l]])),e(t,l,o,c,d)}}(e,b,m,r,a)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,s.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);E&&(p=E,m=b,d=!0)}return!!d&&(f.patchedTree=p,f.cache=m,f.canonicalUrl=g,f.hashFragment=c.hash,(0,l.handleMutable)(t,f))}function h(e,t){let[r,o,...i]=e;if(r.includes(n.PAGE_SEGMENT_KEY))return[(0,n.addSearchParamsIfPageSegment)(r,t),o,...i];let a={};for(let[e,r]of Object.entries(o))a[e]=h(r,t);return[r,a,...i]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return N},createPrefetchURL:function(){return S},default:function(){return w},isExternalURL:function(){return O}});let n=r(740),o=r(687),i=n._(r(3210)),a=r(2142),u=r(9154),s=r(7391),l=r(449),c=r(9129),h=n._(r(5656)),f=r(5416),d=r(6127),p=r(7022),m=r(7086),g=r(4397),y=r(9330),E=r(5942),b=r(6736),_=r(642),v=r(2776),R=r(3690),T=r(6875),P=r(7860);r(3406);let A={};function O(e){return e.origin!==window.location.origin}function S(e){let t;if((0,f.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,d.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return O(t)?null:t}function M(e){let{appRouterState:t}=e;return(0,i.useInsertionEffect)(()=>{let{tree:e,pushRef:r,canonicalUrl:n}=t,o={...r.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};r.pendingPush&&(0,s.createHrefFromUrl)(new URL(window.location.href))!==n?(r.pendingPush=!1,window.history.pushState(o,"",n)):window.history.replaceState(o,"",n)},[t]),(0,i.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function N(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function C(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,o=null!==n?n:r;return(0,i.useDeferredValue)(r,o)}function I(e){let t,{actionQueue:r,assetPrefix:n,globalError:s}=e,f=(0,c.useActionQueue)(r),{canonicalUrl:d}=f,{searchParams:v,pathname:O}=(0,i.useMemo)(()=>{let e=new URL(d,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,E.removeBasePath)(e.pathname):e.pathname}},[d]);(0,i.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(A.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:u.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,i.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,P.isRedirectError)(t)){e.preventDefault();let r=(0,T.getURLFromRedirectError)(t);(0,T.getRedirectTypeFromError)(t)===P.RedirectType.push?R.publicAppRouterInstance.push(r,{}):R.publicAppRouterInstance.replace(r,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:S}=f;if(S.mpaNavigation){if(A.pendingMpaPath!==d){let e=window.location;S.pendingPush?e.assign(d):e.replace(d),A.pendingMpaPath=d}(0,i.use)(y.unresolvedThenable)}(0,i.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,i.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:u.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=H(t),o&&r(o)),e(t,n,o)},window.history.replaceState=function(e,n,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=H(e),o&&r(o)),t(e,n,o)};let n=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,i.startTransition)(()=>{(0,R.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[]);let{cache:N,tree:I,nextUrl:w,focusAndScrollRef:L}=f,B=(0,i.useMemo)(()=>(0,g.findHeadInCache)(N,I[1]),[N,I]),U=(0,i.useMemo)(()=>(0,_.getSelectedParams)(I),[I]),D=(0,i.useMemo)(()=>({parentTree:I,parentCacheNode:N,parentSegmentPath:null,url:d}),[I,N,d]),x=(0,i.useMemo)(()=>({tree:I,focusAndScrollRef:L,nextUrl:w}),[I,L,w]);if(null!==B){let[e,r]=B;t=(0,o.jsx)(C,{headCacheNode:e},r)}else t=null;let F=(0,o.jsxs)(m.RedirectBoundary,{children:[t,N.rsc,(0,o.jsx)(p.AppRouterAnnouncer,{tree:I})]});return F=(0,o.jsx)(h.ErrorBoundary,{errorComponent:s[0],errorStyles:s[1],children:F}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(M,{appRouterState:f}),(0,o.jsx)(j,{}),(0,o.jsx)(l.PathParamsContext.Provider,{value:U,children:(0,o.jsx)(l.PathnameContext.Provider,{value:O,children:(0,o.jsx)(l.SearchParamsContext.Provider,{value:v,children:(0,o.jsx)(a.GlobalLayoutRouterContext.Provider,{value:x,children:(0,o.jsx)(a.AppRouterContext.Provider,{value:R.publicAppRouterInstance,children:(0,o.jsx)(a.LayoutRouterContext.Provider,{value:D,children:F})})})})})})]})}function w(e){let{actionQueue:t,globalErrorComponentAndStyles:[r,n],assetPrefix:i}=e;return(0,v.useNavFailureHandler)(),(0,o.jsx)(h.ErrorBoundary,{errorComponent:h.default,children:(0,o.jsx)(I,{actionQueue:t,assetPrefix:i,globalError:[r,n]})})}let L=new Set,B=new Set;function j(){let[,e]=i.default.useState(0),t=L.size;return(0,i.useEffect)(()=>{let r=()=>e(e=>e+1);return B.add(r),t!==L.size&&r(),()=>{B.delete(r)}},[t,e]),[...L].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&B.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9769:(e,t,r)=>{"use strict";r.d(t,{A:()=>b});var n=r(1120),o=r(4604);function i(e){return e.includes("[[...")}function a(e){return e.includes("[...")}function u(e){return e.includes("[")}function s(e){return"function"==typeof e.then}r(9933);var l=r(6280);r(6294);let c=(0,n.cache)(function(){return{locale:void 0}}),h=(0,n.cache)(async function(){let e=(0,l.b)();return s(e)?await e:e}),f=(0,n.cache)(async function(){let e;try{e=(await h()).get("X-NEXT-INTL-LOCALE")||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function d(){return c().locale||await f()}var p=r(6140);let m=(0,n.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),g=(0,n.cache)(async function(e,t){let r=e({locale:t,get requestLocale(){return t?Promise.resolve(t):d()}});if(s(r)&&(r=await r),!r.locale)throw Error("No locale was returned from `getRequestConfig`.\n\nSee https://next-intl.dev/docs/usage/configuration#i18n-request");return r}),y=(0,n.cache)(o.b),E=(0,n.cache)(o.d),b=(0,n.cache)(async function(e){let t=await g(p.Ay,e);return{...(0,o.i)(t),_formatters:y(E()),timeZone:t.timeZone||m()}})},9916:(e,t,r)=>{"use strict";var n=r(7576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}})},9933:(e,t,r)=>{"use strict";let n=r(4069),o=r(3158),i=r(9294),a=r(3033),u=r(4971),s=r(23),l=r(8388),c=r(6926),h=(r(4523),r(8719)),f=new WeakMap;function d(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):y.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):E.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function p(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let m=(0,c.createDedupedByCallsiteServerErrorLoggerDev)(g);function g(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function y(){return this.getAll().map(e=>[e.name,e]).values()}function E(e){for(let e of this.getAll())this.delete(e.name);return e}}};