[{"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\media\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\projects\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\structure\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\ClientOnly.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\DebugPanel.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\ErrorBoundary.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Footer.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Navigation.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\NoSSR.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\FeaturedNews.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\HeroSection.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\HistoricalTimeline.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\LeadershipCarousel.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\NewsFilters.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\NewsGrid.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\StatisticsDashboard.tsx": "22"}, {"size": 3514, "mtime": 1749998754489, "results": "23", "hashOfConfig": "24"}, {"size": 2785, "mtime": 1750001875000, "results": "25", "hashOfConfig": "24"}, {"size": 2040, "mtime": 1750013462610, "results": "26", "hashOfConfig": "24"}, {"size": 2821, "mtime": 1750001892424, "results": "27", "hashOfConfig": "24"}, {"size": 2174, "mtime": 1749998754473, "results": "28", "hashOfConfig": "24"}, {"size": 3281, "mtime": 1749998754479, "results": "29", "hashOfConfig": "24"}, {"size": 2161, "mtime": 1750012408475, "results": "30", "hashOfConfig": "24"}, {"size": 2870, "mtime": 1750001903733, "results": "31", "hashOfConfig": "24"}, {"size": 2907, "mtime": 1750001915761, "results": "32", "hashOfConfig": "24"}, {"size": 556, "mtime": 1750013219095, "results": "33", "hashOfConfig": "24"}, {"size": 5603, "mtime": 1750013443273, "results": "34", "hashOfConfig": "24"}, {"size": 2146, "mtime": 1750013411775, "results": "35", "hashOfConfig": "24"}, {"size": 8186, "mtime": 1750013744472, "results": "36", "hashOfConfig": "24"}, {"size": 6216, "mtime": 1750013303409, "results": "37", "hashOfConfig": "24"}, {"size": 537, "mtime": 1750013318860, "results": "38", "hashOfConfig": "24"}, {"size": 9364, "mtime": 1749998761722, "results": "39", "hashOfConfig": "24"}, {"size": 6558, "mtime": 1749998761711, "results": "40", "hashOfConfig": "24"}, {"size": 11144, "mtime": 1749998761713, "results": "41", "hashOfConfig": "24"}, {"size": 10450, "mtime": 1749998761725, "results": "42", "hashOfConfig": "24"}, {"size": 11099, "mtime": 1749998761727, "results": "43", "hashOfConfig": "24"}, {"size": 17122, "mtime": 1749998761715, "results": "44", "hashOfConfig": "24"}, {"size": 11469, "mtime": 1749998761729, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k8or59", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\layout.tsx", ["112", "113"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\media\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\page.tsx", ["114", "115", "116", "117"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\structure\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\ClientOnly.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\DebugPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\NoSSR.tsx", ["118"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\FeaturedNews.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\HistoricalTimeline.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\LeadershipCarousel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\NewsFilters.tsx", ["119", "120"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\NewsGrid.tsx", ["121"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\StatisticsDashboard.tsx", [], [], {"ruleId": "122", "severity": 2, "message": "123", "line": 22, "column": 35, "nodeType": "124", "messageId": "125", "endLine": 22, "endColumn": 38, "suggestions": "126"}, {"ruleId": "127", "severity": 1, "message": "128", "line": 44, "column": 9, "nodeType": "129", "endLine": 47, "endColumn": 11}, {"ruleId": "130", "severity": 2, "message": "131", "line": 42, "column": 11, "nodeType": "129", "endLine": 45, "endColumn": 12}, {"ruleId": "130", "severity": 2, "message": "131", "line": 42, "column": 11, "nodeType": "129", "endLine": 45, "endColumn": 12}, {"ruleId": "130", "severity": 2, "message": "132", "line": 48, "column": 11, "nodeType": "129", "endLine": 51, "endColumn": 12}, {"ruleId": "130", "severity": 2, "message": "132", "line": 48, "column": 11, "nodeType": "129", "endLine": 51, "endColumn": 12}, {"ruleId": "133", "severity": 2, "message": "134", "line": 15, "column": 28, "nodeType": null, "messageId": "135", "endLine": 15, "endColumn": 36}, {"ruleId": "136", "severity": 2, "message": "137", "line": 246, "column": 17, "nodeType": "138", "messageId": "139", "suggestions": "140"}, {"ruleId": "136", "severity": 2, "message": "137", "line": 246, "column": 31, "nodeType": "138", "messageId": "139", "suggestions": "141"}, {"ruleId": "142", "severity": 1, "message": "143", "line": 40, "column": 9, "nodeType": "144", "endLine": 131, "endColumn": 4}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["145", "146"], "@next/next/no-page-custom-font", "Custom fonts not added in `pages/_document.js` will only load for a single page. This is discouraged. See: https://nextjs.org/docs/messages/no-page-custom-font", "JSXOpeningElement", "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/ar/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/en/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-unused-vars", "'fallback' is assigned a value but never used.", "unusedVar", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["147", "148", "149", "150"], ["151", "152", "153", "154"], "react-hooks/exhaustive-deps", "The 'allNews' array makes the dependencies of useMemo Hook (at line 157) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'allNews' in its own useMemo() Hook.", "VariableDeclarator", {"messageId": "155", "fix": "156", "desc": "157"}, {"messageId": "158", "fix": "159", "desc": "160"}, {"messageId": "161", "data": "162", "fix": "163", "desc": "164"}, {"messageId": "161", "data": "165", "fix": "166", "desc": "167"}, {"messageId": "161", "data": "168", "fix": "169", "desc": "170"}, {"messageId": "161", "data": "171", "fix": "172", "desc": "173"}, {"messageId": "161", "data": "174", "fix": "175", "desc": "164"}, {"messageId": "161", "data": "176", "fix": "177", "desc": "167"}, {"messageId": "161", "data": "178", "fix": "179", "desc": "170"}, {"messageId": "161", "data": "180", "fix": "181", "desc": "173"}, "suggestUnknown", {"range": "182", "text": "183"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "184", "text": "185"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "186"}, {"range": "187", "text": "188"}, "Replace with `&quot;`.", {"alt": "189"}, {"range": "190", "text": "191"}, "Replace with `&ldquo;`.", {"alt": "192"}, {"range": "193", "text": "194"}, "Replace with `&#34;`.", {"alt": "195"}, {"range": "196", "text": "197"}, "Replace with `&rdquo;`.", {"alt": "186"}, {"range": "198", "text": "199"}, {"alt": "189"}, {"range": "200", "text": "201"}, {"alt": "192"}, {"range": "202", "text": "203"}, {"alt": "195"}, {"range": "204", "text": "205"}, [634, 637], "unknown", [634, 637], "never", "&quot;", [10601, 10619], "\n                &quot;", "&ldquo;", [10601, 10619], "\n                &ldquo;", "&#34;", [10601, 10619], "\n                &#34;", "&rdquo;", [10601, 10619], "\n                &rdquo;", [10632, 10648], "&quot;\n              ", [10632, 10648], "&ldquo;\n              ", [10632, 10648], "&#34;\n              ", [10632, 10648], "&rdquo;\n              "]