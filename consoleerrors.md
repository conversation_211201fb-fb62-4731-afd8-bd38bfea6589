GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
(anonymous) @ content-script-utils.js:18
(anonymous) @ content-script-utils.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
fireWith @ jquery-3.1.1.min.js:2
fire @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
fireWith @ jquery-3.1.1.min.js:2
ready @ jquery-3.1.1.min.js:2
R @ jquery-3.1.1.min.js:3
layout.tsx:29 A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:

- A server/client branch `if (typeof window !== 'undefined')`.
- Variable input such as `Date.now()` or `Math.random()` which changes each time it's called.
- Date formatting in a user's locale which doesn't match the server.
- External changing data without sending a snapshot of it along with the HTML.
- Invalid HTML tag nesting.

It can also happen if the client has a browser extension installed which messes with the HTML before React loaded.

https://react.dev/link/hydration-mismatch

  ...
    <OuterLayoutRouter parallelRouterKey="children" template={<RenderFromTemplateContext>} notFound={[...]}>
      <RenderFromTemplateContext>
        <ScrollAndFocusHandler segmentPath={[...]}>
          <InnerScrollAndFocusHandler segmentPath={[...]} focusAndScrollRef={{apply:false, ...}}>
            <ErrorBoundary errorComponent={undefined} errorStyles={undefined} errorScripts={undefined}>
              <LoadingBoundary loading={null}>
                <HTTPAccessFallbackBoundary notFound={[...]} forbidden={undefined} unauthorized={undefined}>
                  <HTTPAccessFallbackErrorBoundary pathname="/en" notFound={[...]} forbidden={undefined} ...>
                    <RedirectBoundary>
                      <RedirectErrorBoundary router={{...}}>
                        <InnerLayoutRouter url="/en" tree={[...]} cacheNode={{lazyData:null, ...}} segmentPath={[...]}>
                          <link>
                          <LocaleLayout>
                            <html
                              lang="en"
                              dir="ltr"
-                             suppresshydrationwarning="true"
-                             data-lt-installed="true"
                            >

error @ intercept-console-error.js:50
eval @ react-dom-client.development.js:4626
runWithFiberInDEV @ react-dom-client.development.js:845
emitPendingHydrationWarnings @ react-dom-client.development.js:4625
completeWork @ react-dom-client.development.js:11257
runWithFiberInDEV @ react-dom-client.development.js:848
completeUnitOfWork @ react-dom-client.development.js:15394
performUnitOfWork @ react-dom-client.development.js:15275
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<html>
LocaleLayout @ layout.tsx:29
eval @ react-server-dom-webpack-client.browser.development.js:2355
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
readChunk @ react-server-dom-webpack-client.browser.development.js:949
react-stack-bottom-frame @ react-dom-client.development.js:23078
createChild @ react-dom-client.development.js:5383
reconcileChildrenArray @ react-dom-client.development.js:5690
reconcileChildFibersImpl @ react-dom-client.development.js:6013
eval @ react-dom-client.development.js:6118
reconcileChildren @ react-dom-client.development.js:8655
beginWork @ react-dom-client.development.js:10904
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<LocaleLayout>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1750013005955:160
options.factory @ webpack.js?v=1750013005955:712
__webpack_require__ @ webpack.js?v=1750013005955:37
fn @ webpack.js?v=1750013005955:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1750013005955:182
options.factory @ webpack.js?v=1750013005955:712
__webpack_require__ @ webpack.js?v=1750013005955:37
__webpack_exec__ @ main-app.js?v=1750013005955:2813
(anonymous) @ main-app.js?v=1750013005955:2814
webpackJsonpCallback @ webpack.js?v=1750013005955:1388
(anonymous) @ main-app.js?v=1750013005955:9
GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
isEligible @ ActionableCoachmark.js:18
getRenderPrompt @ ShowOneChild.js:18
await in getRenderPrompt
render @ ShowOneChild.js:18
(anonymous) @ ch-content-script-dend.js:18
await in (anonymous)
(anonymous) @ ch-content-script-dend.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
add @ jquery-3.1.1.min.js:2
(anonymous) @ jquery-3.1.1.min.js:2
Deferred @ jquery-3.1.1.min.js:2
then @ jquery-3.1.1.min.js:2
r.fn.ready @ jquery-3.1.1.min.js:2
(anonymous) @ ch-content-script-dend.js:18
