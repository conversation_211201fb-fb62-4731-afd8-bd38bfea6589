'use client';

import { useTranslations, useLocale } from 'next-intl';
import Link from 'next/link';
import { 
  MapPinIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function Footer() {
  const t = useTranslations('footer');
  const locale = useLocale();

  const quickLinks = [
    { key: 'home', href: `/${locale}` as const },
    { key: 'about', href: `/${locale}/about` as const },
    { key: 'news', href: `/${locale}/news` as const },
    { key: 'contact', href: `/${locale}/contact` as const },
  ];

  const legalLinks = [
    { key: 'privacy', href: `/${locale}/privacy` as const },
    { key: 'terms', href: `/${locale}/terms` as const },
    { key: 'accessibility', href: `/${locale}/accessibility` as const },
  ];

  return (
    <footer className="bg-military-black border-t border-steel-gray/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          
          {/* Ministry Logo & Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
              <div className="w-12 h-12 bg-military-green rounded-full flex items-center justify-center military-glow">
                <span className="text-pure-white font-bold text-lg">
                  {locale === 'ar' ? 'ود' : 'MD'}
                </span>
              </div>
              <div>
                <h3 className={`text-lg font-semibold text-pure-white ${
                  locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
                }`}>
                  {locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'}
                </h3>
              </div>
            </div>
            <p className={`text-light-gray text-sm leading-relaxed ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {locale === 'ar' 
                ? 'حماية الوطن والمواطن من خلال قوات مسلحة حديثة ومتطورة'
                : 'Protecting Nation and Citizens through modern and advanced armed forces'
              }
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('quickLinks')}
            </h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.key}>
                  <Link
                    href={link.href as any}
                    className="text-light-gray hover:text-bright-green transition-colors duration-200 text-sm"
                  >
                    {link.key === 'home' ? (locale === 'ar' ? 'الرئيسية' : 'Home') :
                     link.key === 'about' ? (locale === 'ar' ? 'عن الوزارة' : 'About') :
                     link.key === 'news' ? (locale === 'ar' ? 'الأخبار' : 'News') :
                     link.key === 'contact' ? (locale === 'ar' ? 'اتصل بنا' : 'Contact') : link.key}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('contactInfo')}
            </h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <MapPinIcon className="w-5 h-5 text-military-green mt-0.5 flex-shrink-0" />
                <div className="text-light-gray text-sm">
                  <p>{locale === 'ar' ? 'دمشق، الجمهورية العربية السورية' : 'Damascus, Syrian Arab Republic'}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <PhoneIcon className="w-5 h-5 text-military-green flex-shrink-0" />
                <span className="text-light-gray text-sm">+963-11-XXXXXXX</span>
              </div>
              
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <EnvelopeIcon className="w-5 h-5 text-military-green flex-shrink-0" />
                <span className="text-light-gray text-sm"><EMAIL></span>
              </div>
              
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <ClockIcon className="w-5 h-5 text-military-green mt-0.5 flex-shrink-0" />
                <div className="text-light-gray text-sm">
                  <p>{locale === 'ar' ? 'الأحد - الخميس: 8:00 - 16:00' : 'Sunday - Thursday: 8:00 - 16:00'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Legal & Social */}
          <div>
            <h4 className={`text-lg font-semibold text-pure-white mb-4 ${
              locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
            }`}>
              {t('legal')}
            </h4>
            <ul className="space-y-2 mb-6">
              {legalLinks.map((link) => (
                <li key={link.key}>
                  <Link
                    href={link.href as any}
                    className="text-light-gray hover:text-bright-green transition-colors duration-200 text-sm"
                  >
                    {t(link.key)}
                  </Link>
                </li>
              ))}
            </ul>

            {/* Social Media */}
            <div>
              <h5 className={`text-sm font-semibold text-pure-white mb-3 ${
                locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'
              }`}>
                {t('socialMedia')}
              </h5>
              <div className="flex space-x-3 rtl:space-x-reverse">
                <a
                  href="#"
                  className="w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200"
                  aria-label="Facebook"
                >
                  <span className="text-xs text-pure-white font-bold">f</span>
                </a>
                <a
                  href="#"
                  className="w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200"
                  aria-label="Twitter"
                >
                  <span className="text-xs text-pure-white font-bold">𝕏</span>
                </a>
                <a
                  href="#"
                  className="w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200"
                  aria-label="YouTube"
                >
                  <span className="text-xs text-pure-white font-bold">▶</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-steel-gray/30 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className={`text-muted-gray text-sm ${
              locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
            }`}>
              {t('copyright')}
            </p>
            
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <span className={`text-muted-gray text-xs ${
                locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'
              }`}>
                {locale === 'ar' ? 'آخر تحديث: ديسمبر 2024' : 'Last Updated: December 2024'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
