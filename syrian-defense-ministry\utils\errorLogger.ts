/**
 * Error logging utility for the Syrian Defense Ministry website
 */

export interface ErrorLog {
  timestamp: string;
  type: 'hydration' | 'network' | 'extension' | 'runtime' | 'unknown';
  message: string;
  stack?: string;
  url?: string;
  userAgent?: string;
  locale?: string;
}

class ErrorLogger {
  private logs: ErrorLog[] = [];
  private maxLogs = 100;

  constructor() {
    if (typeof window !== 'undefined') {
      this.setupGlobalErrorHandlers();
    }
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.log({
        type: 'runtime',
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
      });
    });

    // Handle general JavaScript errors
    window.addEventListener('error', (event) => {
      // Filter out extension-related errors
      if (this.isExtensionError(event.message || '')) {
        return;
      }

      this.log({
        type: 'runtime',
        message: event.message || 'Unknown error',
        stack: event.error?.stack,
        url: event.filename,
      });
    });

    // Handle React hydration errors specifically
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      
      if (this.isHydrationError(message)) {
        this.log({
          type: 'hydration',
          message: 'React hydration mismatch detected',
          stack: new Error().stack,
        });
      } else if (!this.isExtensionError(message)) {
        // Log non-extension errors
        this.log({
          type: 'runtime',
          message,
        });
      }

      // Call original console.error for non-extension errors
      if (!this.isExtensionError(message)) {
        originalConsoleError.apply(console, args);
      }
    };
  }

  private isExtensionError(message: string): boolean {
    const extensionPatterns = [
      'GenAIWebpageEligibilityService',
      'content-script-utils',
      'porn-domains',
      'block.txt',
      'jquery-3.1.1.min.js',
      'chrome-extension://',
      'moz-extension://',
      'safari-extension://',
    ];

    return extensionPatterns.some(pattern => message.includes(pattern));
  }

  private isHydrationError(message: string): boolean {
    const hydrationPatterns = [
      'hydrated but some attributes',
      'server rendered HTML didn\'t match',
      'suppresshydrationwarning',
      'data-lt-installed',
    ];

    return hydrationPatterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()));
  }

  public log(error: Partial<ErrorLog>) {
    const errorLog: ErrorLog = {
      timestamp: new Date().toISOString(),
      type: error.type || 'unknown',
      message: error.message || 'Unknown error',
      stack: error.stack,
      url: error.url || (typeof window !== 'undefined' ? window.location.href : ''),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : '',
      locale: typeof window !== 'undefined' ? window.location.pathname.split('/')[1] : '',
      ...error,
    };

    this.logs.push(errorLog);

    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // In development, log to console
    if (process.env.NODE_ENV === 'development') {
      console.warn('Error logged:', errorLog);
    }
  }

  public getLogs(): ErrorLog[] {
    return [...this.logs];
  }

  public getLogsByType(type: ErrorLog['type']): ErrorLog[] {
    return this.logs.filter(log => log.type === type);
  }

  public clearLogs() {
    this.logs = [];
  }

  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

// Create singleton instance
export const errorLogger = new ErrorLogger();

// Export for use in components
export default errorLogger;
