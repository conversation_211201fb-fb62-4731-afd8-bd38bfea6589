(()=>{var e={};e.id=429,e.ids=[429],e.modules={273:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Footer.tsx","default")},466:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Navigation.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1916:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(5239),n=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["news",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5634)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2121)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\news\\[id]\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/news/[id]/page",pathname:"/[locale]/news/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4536:(e,t,r)=>{let{createProxy:s}=r(9844);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4858:(e,t,r)=>{Promise.resolve().then(r.bind(r,273)),Promise.resolve().then(r.bind(r,466)),Promise.resolve().then(r.t.bind(r,4536,23))},5026:(e,t,r)=>{Promise.resolve().then(r.bind(r,919)),Promise.resolve().then(r.bind(r,7748)),Promise.resolve().then(r.t.bind(r,5814,23))},5634:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(7413),n=r(466),a=r(273),i=r(4536),o=r.n(i),l=r(1120);let d=l.forwardRef(function({title:e,titleId:t,...r},s){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))});async function c({params:e}){let{locale:t,id:r}=await e;return(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)(n.default,{}),(0,s.jsx)("section",{className:"pt-20 pb-8 bg-military-black/50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(o(),{href:`/${t}/news`,className:"inline-flex items-center text-military-green hover:text-bright-green transition-colors duration-200",children:[(0,s.jsx)(d,{className:`w-5 h-5 mr-2 rtl:mr-0 rtl:ml-2 ${"ar"===t?"rotate-180":""}`}),(0,s.jsx)("span",{className:"ar"===t?"font-arabic-secondary":"font-english-secondary",children:"ar"===t?"العودة إلى الأخبار":"Back to News"})]})})}),(0,s.jsx)("section",{className:"py-16 bg-deep-charcoal camo-pattern",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 lg:p-12 border border-smoke-gray/30 military-glow",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-military-green rounded-full flex items-center justify-center mx-auto mb-6 military-glow-strong",children:(0,s.jsx)("span",{className:"text-3xl font-bold text-pure-white",children:"ar"===t?"خ":"N"})}),(0,s.jsx)("h1",{className:`text-3xl md:text-4xl font-bold text-pure-white mb-4 ${"ar"===t?"font-arabic-headings":"font-english-primary"}`,children:"ar"===t?"تفاصيل الخبر":"News Details"}),(0,s.jsx)("p",{className:`text-light-gray text-lg ${"ar"===t?"font-arabic-secondary":"font-english-secondary"}`,children:"ar"===t?`معرف الخبر: ${r}`:`News ID: ${r}`})]}),(0,s.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,s.jsx)("p",{className:`text-light-gray leading-relaxed text-lg ${"ar"===t?"font-arabic-secondary":"font-english-secondary"}`,children:"ar"===t?"نعمل حالياً على تطوير صفحات تفاصيل الأخبار التي ستعرض المحتوى الكامل للأخبار والتحديثات مع الصور والمعلومات التفصيلية.":"We are currently developing news detail pages that will display the full content of news and updates with images and detailed information."})})]})})}),(0,s.jsx)(a.default,{})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,825,668,57,23,686],()=>r(1916));module.exports=s})();