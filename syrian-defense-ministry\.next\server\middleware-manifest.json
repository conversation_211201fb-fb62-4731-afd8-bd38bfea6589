{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "iLAbAQzPRItxfQB5PrurI", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ubNesKCeCEg8ndjLiqoM2yMbqEUvkaIvXS2443i1omo=", "__NEXT_PREVIEW_MODE_ID": "d0e170d68b457c3d94d7a197b95bbbb7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "dbf3658fd20d9d03911ae67868e4f19a2bf308f421046e8e2a4d7dd3e4b18ce6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fc857f9c16b7855f2bd29ff0f59d69b93df79dc2f895a06569be4a58826bf82d"}}}, "functions": {}, "sortedMiddleware": ["/"]}