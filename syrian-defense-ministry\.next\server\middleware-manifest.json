{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "DJKPVeorXVmbtQec-0Uzo", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ubNesKCeCEg8ndjLiqoM2yMbqEUvkaIvXS2443i1omo=", "__NEXT_PREVIEW_MODE_ID": "42f1f6a3b15d23399b4d135a32755c02", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4b18c5839144595ac56ffd9cfee2af09aabc4d41b44d391159067ef3c52493c3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad8e9cdf22bcab113f703c7838e6bd9f5e6b7349c2672c46322f3f665929a031"}}}, "functions": {}, "sortedMiddleware": ["/"]}