(()=>{var e={};e.id=465,e.ids=[465],e.modules={563:(e,r,s)=>{"use strict";s.d(r,{A:()=>o});var t=s(1120),n=s(4604),a=(0,t.cache)(function(e,r){return function({_cache:e=(0,n.d)(),_formatters:r=(0,n.b)(e),getMessageFallback:s=n.f,messages:t,namespace:a,onError:i=n.g,...o}){return function({messages:e,namespace:r,...s},t){return e=e["!"],r=(0,n.r)(r,"!"),(0,n.e)({...s,messages:e,namespace:r})}({...o,onError:i,cache:e,formatters:r,getMessageFallback:s,messages:{"!":t},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:r})}),i=s(8692);function o(...[e]){return a((0,i.A)("useTranslations"),e)}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1862:(e,r,s)=>{Promise.resolve().then(s.bind(s,5196))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3694:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(7413),n=s(563);function a(){let e=(0,n.A)("site");return(0,t.jsx)("div",{className:"min-h-screen bg-gray-900 text-white p-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,t.jsx)("h1",{className:"text-6xl font-bold mb-8 text-green-400",children:e("title")}),(0,t.jsx)("p",{className:"text-2xl mb-8 text-gray-300",children:e("tagline")}),(0,t.jsxs)("div",{className:"bg-green-800 p-8 rounded-lg shadow-2xl",children:[(0,t.jsxs)("h2",{className:"text-3xl font-bold mb-6 text-white",children:["\uD83C\uDF89 ",e("description")]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-left",children:[(0,t.jsxs)("div",{className:"bg-green-700 p-4 rounded",children:[(0,t.jsx)("h3",{className:"font-bold text-lg",children:"✅ Next.js"}),(0,t.jsx)("p",{children:"App Router Working"})]}),(0,t.jsxs)("div",{className:"bg-green-700 p-4 rounded",children:[(0,t.jsx)("h3",{className:"font-bold text-lg",children:"✅ Internationalization"}),(0,t.jsx)("p",{children:"Arabic/English Support"})]}),(0,t.jsxs)("div",{className:"bg-green-700 p-4 rounded",children:[(0,t.jsx)("h3",{className:"font-bold text-lg",children:"✅ Tailwind CSS"}),(0,t.jsx)("p",{children:"Styling System"})]}),(0,t.jsxs)("div",{className:"bg-green-700 p-4 rounded",children:[(0,t.jsx)("h3",{className:"font-bold text-lg",children:"✅ TypeScript"}),(0,t.jsx)("p",{children:"Type Safety"})]})]})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("a",{href:"/ar",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg mx-2 transition-colors",children:"العربية"}),(0,t.jsx)("a",{href:"/en",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg mx-2 transition-colors",children:"English"})]}),(0,t.jsxs)("div",{className:"mt-8 text-lg",children:[(0,t.jsx)("p",{children:"\uD83D\uDE80 Syrian Defense Ministry Website"}),(0,t.jsx)("p",{children:"\uD83D\uDD27 Development Server Running"}),(0,t.jsx)("p",{children:"\uD83C\uDF10 Ready for Production"})]})]})})}},3873:e=>{"use strict";e.exports=require("path")},7463:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(5239),n=s(8088),a=s(8170),i=s.n(a),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["[locale]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3694)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2121)),"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\app\\[locale]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8692:(e,r,s)=>{"use strict";s.d(r,{A:()=>i});var t=s(9769),n=s(1120),a=s.t(n,2)["use".trim()];function i(e){var r=(0,t.A)();try{return a(r)}catch(r){throw r instanceof TypeError&&r.message.includes("Cannot read properties of null (reading 'use')")?Error(`\`${e}\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`,{cause:r}):r}}},8814:(e,r,s)=>{Promise.resolve().then(s.bind(s,994))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,825,535,23],()=>s(7463));module.exports=t})();