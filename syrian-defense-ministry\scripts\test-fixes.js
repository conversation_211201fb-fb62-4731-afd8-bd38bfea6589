/**
 * Test script to verify console error fixes
 * Run this script to test both Arabic and English routes
 */

const puppeteer = require('puppeteer');

async function testRoute(page, url, locale) {
  console.log(`\n🧪 Testing ${locale.toUpperCase()} route: ${url}`);
  
  const errors = [];
  const warnings = [];
  
  // Capture console messages
  page.on('console', msg => {
    const text = msg.text();
    if (msg.type() === 'error') {
      // Filter out extension-related errors
      if (!text.includes('GenAIWebpageEligibilityService') && 
          !text.includes('content-script-utils') &&
          !text.includes('porn-domains') &&
          !text.includes('block.txt')) {
        errors.push(text);
      }
    } else if (msg.type() === 'warning') {
      warnings.push(text);
    }
  });

  try {
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
    
    // Wait for hydration to complete
    await page.waitForTimeout(2000);
    
    // Check if page loaded correctly
    const title = await page.title();
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    const htmlDir = await page.evaluate(() => document.documentElement.dir);
    
    console.log(`✅ Page loaded successfully`);
    console.log(`   Title: ${title}`);
    console.log(`   Language: ${htmlLang}`);
    console.log(`   Direction: ${htmlDir}`);
    
    // Check for hydration errors
    const hydrationErrors = errors.filter(error => 
      error.includes('hydration') || 
      error.includes('server rendered HTML')
    );
    
    if (hydrationErrors.length === 0) {
      console.log(`✅ No hydration errors detected`);
    } else {
      console.log(`❌ Hydration errors found:`);
      hydrationErrors.forEach(error => console.log(`   - ${error}`));
    }
    
    // Check for other errors
    const otherErrors = errors.filter(error => 
      !error.includes('hydration') && 
      !error.includes('server rendered HTML')
    );
    
    if (otherErrors.length === 0) {
      console.log(`✅ No other errors detected`);
    } else {
      console.log(`⚠️  Other errors found:`);
      otherErrors.forEach(error => console.log(`   - ${error}`));
    }
    
    // Check warnings
    if (warnings.length === 0) {
      console.log(`✅ No warnings detected`);
    } else {
      console.log(`⚠️  Warnings found:`);
      warnings.forEach(warning => console.log(`   - ${warning}`));
    }
    
    return {
      success: hydrationErrors.length === 0,
      errors: errors.length,
      warnings: warnings.length,
      hydrationErrors: hydrationErrors.length
    };
    
  } catch (error) {
    console.log(`❌ Failed to load page: ${error.message}`);
    return {
      success: false,
      errors: errors.length + 1,
      warnings: warnings.length,
      hydrationErrors: 0
    };
  }
}

async function runTests() {
  console.log('🚀 Starting console error fix verification tests...\n');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Test both routes
    const results = [];
    
    // Test Arabic route
    results.push(await testRoute(page, 'http://localhost:3001/ar', 'Arabic'));
    
    // Test English route  
    results.push(await testRoute(page, 'http://localhost:3001/en', 'English'));
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    const totalErrors = results.reduce((sum, r) => sum + r.errors, 0);
    const totalWarnings = results.reduce((sum, r) => sum + r.warnings, 0);
    const totalHydrationErrors = results.reduce((sum, r) => sum + r.hydrationErrors, 0);
    const allSuccessful = results.every(r => r.success);
    
    console.log(`Total Errors: ${totalErrors}`);
    console.log(`Total Warnings: ${totalWarnings}`);
    console.log(`Hydration Errors: ${totalHydrationErrors}`);
    console.log(`Overall Status: ${allSuccessful ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (allSuccessful) {
      console.log('\n🎉 All tests passed! Console errors have been successfully fixed.');
    } else {
      console.log('\n⚠️  Some issues remain. Check the detailed output above.');
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRoute };
