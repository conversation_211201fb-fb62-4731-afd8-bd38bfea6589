/**
 * Browser Extension Interference Prevention Script
 * This script helps prevent browser extensions from interfering with the website
 */

(function() {
  'use strict';

  // Prevent common extension interference
  if (typeof window !== 'undefined') {
    // Block common extension scripts from modifying the DOM
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
      const element = originalCreateElement.call(this, tagName);
      
      // Block suspicious script injections
      if (tagName.toLowerCase() === 'script') {
        const originalSetAttribute = element.setAttribute;
        element.setAttribute = function(name, value) {
          if (name === 'src' && typeof value === 'string') {
            // Block known problematic extension URLs
            const blockedPatterns = [
              'GenAIWebpageEligibilityService',
              'content-script-utils',
              'porn-domains',
              'block.txt'
            ];
            
            if (blockedPatterns.some(pattern => value.includes(pattern))) {
              console.warn('Blocked potentially problematic script:', value);
              return;
            }
          }
          return originalSetAttribute.call(this, name, value);
        };
      }
      
      return element;
    };

    // Suppress console errors from extensions
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      
      // Filter out known extension errors
      const extensionErrors = [
        'GenAIWebpageEligibilityService',
        'content-script-utils',
        'porn-domains',
        'block.txt',
        'jquery-3.1.1.min.js'
      ];
      
      if (extensionErrors.some(pattern => message.includes(pattern))) {
        // Silently ignore extension errors
        return;
      }
      
      // Log other errors normally
      return originalConsoleError.apply(console, args);
    };

    // Prevent extensions from modifying critical attributes
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes') {
          const target = mutation.target;
          
          // Restore critical attributes if modified by extensions
          if (target.tagName === 'HTML') {
            const lang = target.getAttribute('lang');
            const dir = target.getAttribute('dir');
            
            // Ensure language and direction attributes are preserved
            if (!lang || (lang !== 'ar' && lang !== 'en')) {
              const currentPath = window.location.pathname;
              const isArabic = currentPath.startsWith('/ar');
              target.setAttribute('lang', isArabic ? 'ar' : 'en');
              target.setAttribute('dir', isArabic ? 'rtl' : 'ltr');
            }
          }
        }
      });
    });

    // Start observing
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['lang', 'dir', 'data-lt-installed'],
      subtree: false
    });

    // Clean up on page unload
    window.addEventListener('beforeunload', function() {
      observer.disconnect();
    });
  }
})();
