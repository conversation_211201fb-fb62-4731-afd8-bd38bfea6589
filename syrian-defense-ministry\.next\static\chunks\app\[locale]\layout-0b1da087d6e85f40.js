(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{4162:(e,t,r)=>{"use strict";r.d(t,{default:()=>o}),r(5155);var s=r(2115);function o(){let[e,t]=(0,s.useState)(!1),[r,o]=(0,s.useState)([]),[n,i]=(0,s.useState)("all");return(0,s.useEffect)(()=>{},[]),null}r(4818),r(8540)},4818:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});class s{setupGlobalErrorHandlers(){var e=this;window.addEventListener("unhandledrejection",e=>{var t;this.log({type:"runtime",message:"Unhandled Promise Rejection: ".concat(e.reason),stack:null==(t=e.reason)?void 0:t.stack})}),window.addEventListener("error",e=>{var t;this.isExtensionError(e.message||"")||this.log({type:"runtime",message:e.message||"Unknown error",stack:null==(t=e.error)?void 0:t.stack,url:e.filename})});let t=console.error;console.error=function(){for(var r=arguments.length,s=Array(r),o=0;o<r;o++)s[o]=arguments[o];let n=s.join(" ");e.isHydrationError(n)?e.log({type:"hydration",message:"React hydration mismatch detected",stack:Error().stack}):e.isExtensionError(n)||e.log({type:"runtime",message:n}),e.isExtensionError(n)||t.apply(console,s)}}isExtensionError(e){return["GenAIWebpageEligibilityService","content-script-utils","porn-domains","block.txt","jquery-3.1.1.min.js","chrome-extension://","moz-extension://","safari-extension://"].some(t=>e.includes(t))}isHydrationError(e){return["hydrated but some attributes","server rendered HTML didn't match","suppresshydrationwarning","data-lt-installed"].some(t=>e.toLowerCase().includes(t.toLowerCase()))}log(e){let t={timestamp:new Date().toISOString(),type:e.type||"unknown",message:e.message||"Unknown error",stack:e.stack,url:e.url||window.location.href,userAgent:"undefined"!=typeof navigator?navigator.userAgent:"",locale:window.location.pathname.split("/")[1],...e};this.logs.push(t),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs))}getLogs(){return[...this.logs]}getLogsByType(e){return this.logs.filter(t=>t.type===e)}clearLogs(){this.logs=[]}exportLogs(){return JSON.stringify(this.logs,null,2)}constructor(){this.logs=[],this.maxLogs=100,this.setupGlobalErrorHandlers()}}let o=new s},4821:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(5155),o=r(2115),n=r(4818);class i extends o.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){n.F.log({type:"runtime",message:e.message,stack:e.stack}),console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback||(0,s.jsx)("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4 text-red-400",children:"حدث خطأ غير متوقع - An Unexpected Error Occurred"}),(0,s.jsxs)("p",{className:"text-gray-300 mb-6",children:["نعتذر عن هذا الخطأ. يرجى إعادة تحميل الصفحة.",(0,s.jsx)("br",{}),"We apologize for this error. Please reload the page."]}),(0,s.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors",children:"إعادة تحميل - Reload"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1}}}let a=i},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(3385),o=r(5155);function n(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,o.jsx)(s.Dk,{locale:t,...r})}},7042:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9324,23)),Promise.resolve().then(r.bind(r,4162)),Promise.resolve().then(r.bind(r,4821)),Promise.resolve().then(r.bind(r,6096))},8540:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(5155),o=r(2115);function n(e){let{children:t,fallback:r=null}=e,[n,i]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{i(!0)},[]),n)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)(s.Fragment,{children:r})}},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[533,385,441,684,358],()=>t(7042)),_N_E=e.O()}]);