'use client';

import { useState } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bars3Icon,
  XMarkIcon,
  LanguageIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import ClientOnly from '../ClientOnly';

export default function Navigation() {
  const t = useTranslations('navigation');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);

  const navigationItems = [
    { key: 'home', href: `/${locale}` },
    { key: 'about', href: `/${locale}/about` },
    { key: 'structure', href: `/${locale}/structure` },
    { key: 'news', href: `/${locale}/news` },
    { key: 'projects', href: `/${locale}/projects` },
    { key: 'media', href: `/${locale}/media` },
    { key: 'contact', href: `/${locale}/contact` },
  ];

  const switchLanguage = (newLocale: string) => {
    const currentPath = pathname.replace(`/${locale}`, '');
    router.push(`/${newLocale}${currentPath}`);
    setIsLanguageOpen(false);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-military-black/90 backdrop-blur-md border-b border-steel-gray/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center space-x-3 rtl:space-x-reverse">
            <div className="w-10 h-10 bg-military-green rounded-full flex items-center justify-center military-glow">
              <span className="text-pure-white font-bold text-lg">ود</span>
            </div>
            <div className="hidden md:block">
              <h1 className="text-lg font-semibold text-pure-white">
                {locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'}
              </h1>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {navigationItems.map((item) => (
              <Link
                key={item.key}
                // @ts-expect-error - Dynamic locale-based href
                href={item.href}
                className="text-light-gray hover:text-bright-green transition-colors duration-200 text-sm font-medium"
              >
                {t(item.key)}
              </Link>
            ))}
          </div>

          {/* Language Switcher & Mobile Menu Button */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Language Switcher */}
            <div className="relative">
              <button
                type="button"
                onClick={() => setIsLanguageOpen(!isLanguageOpen)}
                className="flex items-center space-x-2 rtl:space-x-reverse text-light-gray hover:text-bright-green transition-colors duration-200"
              >
                <LanguageIcon className="w-5 h-5" />
                <span className="text-sm font-medium">{locale.toUpperCase()}</span>
                <ChevronDownIcon className="w-4 h-4" />
              </button>

              <ClientOnly>
                <AnimatePresence>
                  {isLanguageOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute top-full mt-2 right-0 bg-steel-gray rounded-lg shadow-lg border border-smoke-gray/30 overflow-hidden"
                    >
                      <button
                        type="button"
                        onClick={() => switchLanguage('ar')}
                        className="block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200"
                      >
                        العربية
                      </button>
                      <button
                        type="button"
                        onClick={() => switchLanguage('en')}
                        className="block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200"
                      >
                        English
                      </button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </ClientOnly>
            </div>

            {/* Mobile Menu Button */}
            <button
              type="button"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden text-light-gray hover:text-bright-green transition-colors duration-200"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <ClientOnly>
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-steel-gray/95 backdrop-blur-md border-t border-smoke-gray/30"
            >
              <div className="px-4 py-4 space-y-2">
                {navigationItems.map((item) => (
                  <Link
                    key={item.key}
                    // @ts-expect-error - Dynamic locale-based href
                    href={item.href}
                    onClick={() => setIsMenuOpen(false)}
                    className="block px-4 py-2 text-light-gray hover:text-bright-green hover:bg-smoke-gray/30 rounded-lg transition-all duration-200"
                  >
                    {t(item.key)}
                  </Link>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ClientOnly>
    </nav>
  );
}
