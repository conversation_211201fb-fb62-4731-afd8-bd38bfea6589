exports.id=23,exports.ids=[23],exports.modules={726:(e,t,s)=>{Promise.resolve().then(s.bind(s,7070)),Promise.resolve().then(s.bind(s,3331)),Promise.resolve().then(s.bind(s,5196))},792:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\DebugPanel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\DebugPanel.tsx","default")},2121:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(7413),n=s(9916),o=s(8946),i=s(3930),a=s(6140),l=s(4493),c=s(792);async function d({children:e,params:t}){let{locale:s}=await t;a.IB.includes(s)||(0,n.notFound)();let d=await (0,i.A)({locale:s});return(0,r.jsxs)("html",{lang:s,dir:"ar"===s?"rtl":"ltr",suppressHydrationWarning:!0,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{name:"robots",content:"index, follow"}),(0,r.jsx)("meta",{name:"theme-color",content:"#1a1a1a"}),(0,r.jsx)("title",{children:"وزارة الدفاع السورية - Syrian Defense Ministry"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap",rel:"stylesheet"}),(0,r.jsx)("script",{src:"/scripts/extension-blocker.js",defer:!0})]}),(0,r.jsx)("body",{className:"min-h-screen bg-gray-900 text-white camo-pattern",suppressHydrationWarning:!0,children:(0,r.jsx)(l.default,{children:(0,r.jsxs)(o.A,{messages:d,children:[e,(0,r.jsx)(c.default,{})]})})})]})}s(2704)},2704:()=>{},3331:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(687),n=s(3210),o=s(6492);class i extends n.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){o.F.log({type:"runtime",message:e.message,stack:e.stack}),console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback||(0,r.jsx)("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4 text-red-400",children:"حدث خطأ غير متوقع - An Unexpected Error Occurred"}),(0,r.jsxs)("p",{className:"text-gray-300 mb-6",children:["نعتذر عن هذا الخطأ. يرجى إعادة تحميل الصفحة.",(0,r.jsx)("br",{}),"We apologize for this error. Please reload the page."]}),(0,r.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors",children:"إعادة تحميل - Reload"})]})}):this.props.children}}let a=i},4493:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\ErrorBoundary.tsx","default")},5185:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},6055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6140:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,IB:()=>n});var r=s(5471);let n=["ar","en"],o=(0,r.A)(async({requestLocale:e})=>{let t=await e;return t&&n.includes(t)||(t="ar"),{locale:t,messages:(await s(6565)(`./${t}.json`)).default,timeZone:"Asia/Damascus",now:new Date}})},6492:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});class r{constructor(){this.logs=[],this.maxLogs=100}setupGlobalErrorHandlers(){window.addEventListener("unhandledrejection",e=>{this.log({type:"runtime",message:`Unhandled Promise Rejection: ${e.reason}`,stack:e.reason?.stack})}),window.addEventListener("error",e=>{this.isExtensionError(e.message||"")||this.log({type:"runtime",message:e.message||"Unknown error",stack:e.error?.stack,url:e.filename})});let e=console.error;console.error=(...t)=>{let s=t.join(" ");this.isHydrationError(s)?this.log({type:"hydration",message:"React hydration mismatch detected",stack:Error().stack}):this.isExtensionError(s)||this.log({type:"runtime",message:s}),this.isExtensionError(s)||e.apply(console,t)}}isExtensionError(e){return["GenAIWebpageEligibilityService","content-script-utils","porn-domains","block.txt","jquery-3.1.1.min.js","chrome-extension://","moz-extension://","safari-extension://"].some(t=>e.includes(t))}isHydrationError(e){return["hydrated but some attributes","server rendered HTML didn't match","suppresshydrationwarning","data-lt-installed"].some(t=>e.toLowerCase().includes(t.toLowerCase()))}log(e){let t={timestamp:new Date().toISOString(),type:e.type||"unknown",message:e.message||"Unknown error",stack:e.stack,url:e.url||"",userAgent:"undefined"!=typeof navigator?navigator.userAgent:"",locale:"",...e};this.logs.push(t),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs))}getLogs(){return[...this.logs]}getLogsByType(e){return this.logs.filter(t=>t.type===e)}clearLogs(){this.logs=[]}exportLogs(){return JSON.stringify(this.logs,null,2)}}let n=new r},6565:(e,t,s)=>{var r={"./ar.json":[7552,552],"./en.json":[7368,368]};function n(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return s.e(t[1]).then(()=>s.t(n,19))}n.keys=()=>Object.keys(r),n.id=6565,e.exports=n},7070:(e,t,s)=>{"use strict";s.d(t,{default:()=>n}),s(687);var r=s(3210);function n(){let[e,t]=(0,r.useState)(!1),[s,n]=(0,r.useState)([]),[o,i]=(0,r.useState)("all");return null}s(6492),s(7272)},7272:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(687),n=s(3210);function o({children:e,fallback:t=null}){let[s,o]=(0,n.useState)(!1);return s?(0,r.jsx)(r.Fragment,{children:e}):(0,r.jsx)(r.Fragment,{children:t})}},7678:(e,t,s)=>{Promise.resolve().then(s.bind(s,792)),Promise.resolve().then(s.bind(s,4493)),Promise.resolve().then(s.bind(s,994))},8337:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))}};