@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&family=Cairo:wght@200;300;400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Syrian Ministry of Defense Color Palette */
  --deep-charcoal: #1a1a1a;
  --military-black: #0d0d0d;
  --steel-gray: #2d2d2d;
  --smoke-gray: #404040;
  --military-green: #2d5016;
  --bright-green: #4a7c59;
  --syrian-flag-green: #007a3d;
  --warning-red: #8b0000;
  --pure-white: #ffffff;
  --light-gray: #cccccc;
  --muted-gray: #999999;

  /* Background and foreground */
  --background: var(--deep-charcoal);
  --foreground: var(--pure-white);

  /* Typography */
  --font-arabic-primary: 'Amiri', serif;
  --font-arabic-secondary: 'Noto Sans Arabic', sans-serif;
  --font-arabic-headings: 'Cairo', sans-serif;
  --font-english-primary: 'Inter', sans-serif;
  --font-english-secondary: 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-deep-charcoal: var(--deep-charcoal);
  --color-military-black: var(--military-black);
  --color-steel-gray: var(--steel-gray);
  --color-smoke-gray: var(--smoke-gray);
  --color-military-green: var(--military-green);
  --color-bright-green: var(--bright-green);
  --color-syrian-flag-green: var(--syrian-flag-green);
  --color-warning-red: var(--warning-red);
  --color-pure-white: var(--pure-white);
  --color-light-gray: var(--light-gray);
  --color-muted-gray: var(--muted-gray);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-english-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Arabic text styling */
[dir="rtl"] {
  font-family: var(--font-arabic-primary);
}

[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  font-family: var(--font-arabic-headings);
}

/* Camouflage pattern background */
.camo-pattern {
  background-image:
    radial-gradient(circle at 20% 50%, rgba(45, 45, 45, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(64, 64, 64, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(29, 29, 29, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 60% 60%, rgba(45, 80, 22, 0.05) 0%, transparent 50%);
  background-size: 200px 200px, 150px 150px, 180px 180px, 220px 220px;
  background-position: 0 0, 50px 50px, 100px 0, 150px 100px;
}

/* Ambient glow effects */
.military-glow {
  box-shadow: 0 0 20px rgba(45, 80, 22, 0.3);
}

.military-glow-strong {
  box-shadow: 0 0 30px rgba(45, 80, 22, 0.5), 0 0 60px rgba(45, 80, 22, 0.2);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s ease-in-out;
}

.transition-fast {
  transition: all 0.15s ease-out;
}

.transition-slow {
  transition: all 0.5s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--military-black);
}

::-webkit-scrollbar-thumb {
  background: var(--military-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--bright-green);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Additional responsive utilities */
@media (max-width: 768px) {
  .camo-pattern {
    background-size: 100px 100px, 75px 75px, 90px 90px, 110px 110px;
  }
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid var(--military-green);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(45, 80, 22, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(45, 80, 22, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
