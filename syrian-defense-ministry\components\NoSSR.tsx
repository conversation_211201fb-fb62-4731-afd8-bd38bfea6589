'use client';

import dynamic from 'next/dynamic';
import { ReactNode } from 'react';

interface NoSSRProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * NoSSR component to completely disable server-side rendering
 * for components that have issues with browser extensions or hydration
 */
const NoSSR = ({ children }: NoSSRProps) => {
  return <>{children}</>;
};

// Export as a dynamic component with no SSR
export default dynamic(() => Promise.resolve(NoSSR), {
  ssr: false,
  loading: () => null,
});
