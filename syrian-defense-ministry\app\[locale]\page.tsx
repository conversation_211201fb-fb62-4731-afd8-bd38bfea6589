import { useTranslations } from 'next-intl';
import Link from 'next/link';

export default function HomePage() {
  const t = useTranslations('site');

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-6xl font-bold mb-8 text-green-400">
          {t('title')}
        </h1>
        <p className="text-2xl mb-8 text-gray-300">
          {t('tagline')}
        </p>

        <div className="bg-green-800 p-8 rounded-lg shadow-2xl">
          <h2 className="text-3xl font-bold mb-6 text-white">
            🎉 {t('description')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div className="bg-green-700 p-4 rounded">
              <h3 className="font-bold text-lg">✅ Next.js</h3>
              <p>App Router Working</p>
            </div>
            <div className="bg-green-700 p-4 rounded">
              <h3 className="font-bold text-lg">✅ Internationalization</h3>
              <p>Arabic/English Support</p>
            </div>
            <div className="bg-green-700 p-4 rounded">
              <h3 className="font-bold text-lg">✅ Tailwind CSS</h3>
              <p>Styling System</p>
            </div>
            <div className="bg-green-700 p-4 rounded">
              <h3 className="font-bold text-lg">✅ TypeScript</h3>
              <p>Type Safety</p>
            </div>
          </div>
        </div>

        {/* Language Switcher */}
        <div className="mt-8">
          <Link
            href="/ar"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg mx-2 transition-colors"
          >
            العربية
          </Link>
          <Link
            href="/en"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg mx-2 transition-colors"
          >
            English
          </Link>
        </div>

        <div className="mt-8 text-lg">
          <p>🚀 Syrian Defense Ministry Website</p>
          <p>🔧 Development Server Running</p>
          <p>🌐 Ready for Production</p>
        </div>
      </div>
    </div>
  );
}
