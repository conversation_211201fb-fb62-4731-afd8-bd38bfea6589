/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/about/page";
exports.ids = ["app/[locale]/about/page"];
exports.modules = {

/***/ "(rsc)/./app/[locale]/about/page.tsx":
/*!*************************************!*\
  !*** ./app/[locale]/about/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useTranslations.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/useLocale.js\");\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(rsc)/./components/layout/Navigation.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./components/layout/Footer.tsx\");\n/* harmony import */ var _components_sections_LeadershipCarousel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/LeadershipCarousel */ \"(rsc)/./components/sections/LeadershipCarousel.tsx\");\n/* harmony import */ var _components_sections_HistoricalTimeline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/HistoricalTimeline */ \"(rsc)/./components/sections/HistoricalTimeline.tsx\");\n/* harmony import */ var _components_sections_StatisticsDashboard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/StatisticsDashboard */ \"(rsc)/./components/sections/StatisticsDashboard.tsx\");\n\n\n\n\n\n\n\nfunction AboutPage() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('about');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-20 pb-16 bg-deep-charcoal camo-pattern\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: `text-4xl md:text-5xl lg:text-6xl font-bold text-pure-white mb-6 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                children: t('title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: `text-2xl font-bold text-bright-green mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                            children: t('mission.title')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-light-gray leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                            children: t('mission.description')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: `text-2xl font-bold text-bright-green mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                            children: t('vision.title')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-light-gray leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                            children: t('vision.description')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-steel-gray/50 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: `text-2xl font-bold text-bright-green mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                            children: t('values.title')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: `space-y-2 text-light-gray ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                            children: t.raw('values.items').map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-center space-x-2 rtl:space-x-reverse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-2 h-2 bg-military-green rounded-full flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_LeadershipCarousel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_HistoricalTimeline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_StatisticsDashboard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../i18n */ \"(rsc)/./i18n.ts\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\nasync function LocaleLayout({ children, params }) {\n    const { locale } = await params;\n    // Ensure that the incoming `locale` is valid\n    if (!_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // Providing all messages to the client\n    // side is the easiest way to get started\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        locale\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: locale === 'ar' ? 'rtl' : 'ltr',\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"وزارة الدفاع السورية - Syrian Defense Ministry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gray-900 text-white camo-pattern\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    messages: messages,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"154c6ac6cb60\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW1lcmtcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcZGVmZW5jZSBtaW5pc3RlciBvZiBzeXJpYVxcc3lyaWFuLWRlZmVuc2UtbWluaXN0cnlcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNTRjNmFjNmNiNjBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/layout/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/layout/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\layout\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/sections/HistoricalTimeline.tsx":
/*!****************************************************!*\
  !*** ./components/sections/HistoricalTimeline.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\HistoricalTimeline.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/sections/LeadershipCarousel.tsx":
/*!****************************************************!*\
  !*** ./components/sections/LeadershipCarousel.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\LeadershipCarousel.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/sections/StatisticsDashboard.tsx":
/*!*****************************************************!*\
  !*** ./components/sections/StatisticsDashboard.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\defence minister of syria\\syrian-defense-ministry\\components\\sections\\StatisticsDashboard.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./i18n.ts":
/*!*****************!*\
  !*** ./i18n.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js\");\n\n// Supported locales\nconst locales = [\n    'ar',\n    'en'\n];\nconst defaultLocale = 'ar';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(async ({ requestLocale })=>{\n    // This corresponds to the `[locale]` segment\n    let locale = await requestLocale;\n    // Ensure that the incoming `locale` is valid\n    if (!locale || !locales.includes(locale)) {\n        locale = defaultLocale;\n    }\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./messages lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n        timeZone: 'Asia/Damascus',\n        now: new Date()\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFDb0Q7QUFFcEQsb0JBQW9CO0FBQ2IsTUFBTUMsVUFBVTtJQUFDO0lBQU07Q0FBSyxDQUFVO0FBQ3RDLE1BQU1DLGdCQUFnQixLQUFjO0FBSTNDLGlFQUFlRiw0REFBZ0JBLENBQUMsT0FBTyxFQUFFRyxhQUFhLEVBQUU7SUFDdEQsNkNBQTZDO0lBQzdDLElBQUlDLFNBQVMsTUFBTUQ7SUFFbkIsNkNBQTZDO0lBQzdDLElBQUksQ0FBQ0MsVUFBVSxDQUFDSCxRQUFRSSxRQUFRLENBQUNELFNBQWdCO1FBQy9DQSxTQUFTRjtJQUNYO0lBRUEsT0FBTztRQUNMRTtRQUNBRSxVQUFVLENBQUMsTUFBTSx5RUFBTyxHQUFZLEVBQUVGLE9BQU8sTUFBTSxHQUFHRyxPQUFPO1FBQzdEQyxVQUFVO1FBQ1ZDLEtBQUssSUFBSUM7SUFDWDtBQUNGLEVBQUUsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbWVya1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxkZWZlbmNlIG1pbmlzdGVyIG9mIHN5cmlhXFxzeXJpYW4tZGVmZW5zZS1taW5pc3RyeVxcaTE4bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub3RGb3VuZCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyBnZXRSZXF1ZXN0Q29uZmlnIH0gZnJvbSAnbmV4dC1pbnRsL3NlcnZlcic7XG5cbi8vIFN1cHBvcnRlZCBsb2NhbGVzXG5leHBvcnQgY29uc3QgbG9jYWxlcyA9IFsnYXInLCAnZW4nXSBhcyBjb25zdDtcbmV4cG9ydCBjb25zdCBkZWZhdWx0TG9jYWxlID0gJ2FyJyBhcyBjb25zdDtcblxuZXhwb3J0IHR5cGUgTG9jYWxlID0gKHR5cGVvZiBsb2NhbGVzKVtudW1iZXJdO1xuXG5leHBvcnQgZGVmYXVsdCBnZXRSZXF1ZXN0Q29uZmlnKGFzeW5jICh7IHJlcXVlc3RMb2NhbGUgfSkgPT4ge1xuICAvLyBUaGlzIGNvcnJlc3BvbmRzIHRvIHRoZSBgW2xvY2FsZV1gIHNlZ21lbnRcbiAgbGV0IGxvY2FsZSA9IGF3YWl0IHJlcXVlc3RMb2NhbGU7XG5cbiAgLy8gRW5zdXJlIHRoYXQgdGhlIGluY29taW5nIGBsb2NhbGVgIGlzIHZhbGlkXG4gIGlmICghbG9jYWxlIHx8ICFsb2NhbGVzLmluY2x1ZGVzKGxvY2FsZSBhcyBhbnkpKSB7XG4gICAgbG9jYWxlID0gZGVmYXVsdExvY2FsZTtcbiAgfVxuXG4gIHJldHVybiB7XG4gICAgbG9jYWxlLFxuICAgIG1lc3NhZ2VzOiAoYXdhaXQgaW1wb3J0KGAuL21lc3NhZ2VzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHQsXG4gICAgdGltZVpvbmU6ICdBc2lhL0RhbWFzY3VzJyxcbiAgICBub3c6IG5ldyBEYXRlKClcbiAgfTtcbn0pO1xuIl0sIm5hbWVzIjpbImdldFJlcXVlc3RDb25maWciLCJsb2NhbGVzIiwiZGVmYXVsdExvY2FsZSIsInJlcXVlc3RMb2NhbGUiLCJsb2NhbGUiLCJpbmNsdWRlcyIsIm1lc3NhZ2VzIiwiZGVmYXVsdCIsInRpbWVab25lIiwibm93IiwiRGF0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./i18n.ts\n");

/***/ }),

/***/ "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$":
/*!********************************************************!*\
  !*** ./messages/ lazy ^\.\/.*\.json$ namespace object ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./ar.json": [
		"(rsc)/./messages/ar.json",
		"_rsc_messages_ar_json"
	],
	"./en.json": [
		"(rsc)/./messages/en.json",
		"_rsc_messages_en_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./messages lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\"));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/about/page.tsx */ \"(rsc)/./app/[locale]/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module3, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'not-found': [module0, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module1, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module2, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\app\\\\[locale]\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/about/page\",\n        pathname: \"/[locale]/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2RlZmVuY2UlMjBtaW5pc3RlciUyMG9mJTIwc3lyaWElNUMlNUNzeXJpYW4tZGVmZW5zZS1taW5pc3RyeSU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2RlZmVuY2UlMjBtaW5pc3RlciUyMG9mJTIwc3lyaWElNUMlNUNzeXJpYW4tZGVmZW5zZS1taW5pc3RyeSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNkZXZlbG9wbWVudCU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3UUFBdVAiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxkZWZlbmNlIG1pbmlzdGVyIG9mIHN5cmlhXFxcXHN5cmlhbi1kZWZlbnNlLW1pbmlzdHJ5XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcZGV2ZWxvcG1lbnRcXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(rsc)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Navigation.tsx */ \"(rsc)/./components/layout/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/HistoricalTimeline.tsx */ \"(rsc)/./components/sections/HistoricalTimeline.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/LeadershipCarousel.tsx */ \"(rsc)/./components/sections/LeadershipCarousel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/StatisticsDashboard.tsx */ \"(rsc)/./components/sections/StatisticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbWVya1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxkZWZlbmNlIG1pbmlzdGVyIG9mIHN5cmlhXFxzeXJpYW4tZGVmZW5zZS1taW5pc3RyeVxcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./components/layout/Footer.tsx":
/*!**************************************!*\
  !*** ./components/layout/Footer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,EnvelopeIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Footer() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)('footer');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const quickLinks = [\n        {\n            key: 'home',\n            href: `/${locale}`\n        },\n        {\n            key: 'about',\n            href: `/${locale}/about`\n        },\n        {\n            key: 'news',\n            href: `/${locale}/news`\n        },\n        {\n            key: 'contact',\n            href: `/${locale}/contact`\n        }\n    ];\n    const legalLinks = [\n        {\n            key: 'privacy',\n            href: `/${locale}/privacy`\n        },\n        {\n            key: 'terms',\n            href: `/${locale}/terms`\n        },\n        {\n            key: 'accessibility',\n            href: `/${locale}/accessibility`\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-military-black border-t border-steel-gray/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 rtl:space-x-reverse mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-military-green rounded-full flex items-center justify-center military-glow\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-pure-white font-bold text-lg\",\n                                                children: locale === 'ar' ? 'ود' : 'MD'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: `text-lg font-semibold text-pure-white ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                                children: locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-light-gray text-sm leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                    children: locale === 'ar' ? 'حماية الوطن والمواطن من خلال قوات مسلحة حديثة ومتطورة' : 'Protecting Nation and Citizens through modern and advanced armed forces'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: t('quickLinks')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm\",\n                                                children: link.key === 'home' ? locale === 'ar' ? 'الرئيسية' : 'Home' : link.key === 'about' ? locale === 'ar' ? 'عن الوزارة' : 'About' : link.key === 'news' ? locale === 'ar' ? 'الأخبار' : 'News' : link.key === 'contact' ? locale === 'ar' ? 'اتصل بنا' : 'Contact' : link.key\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: t('contactInfo')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 text-military-green mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-light-gray text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: locale === 'ar' ? 'دمشق، الجمهورية العربية السورية' : 'Damascus, Syrian Arab Republic'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 text-military-green flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-light-gray text-sm\",\n                                                    children: \"+963-11-XXXXXXX\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-military-green flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-light-gray text-sm\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-military-green mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-light-gray text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: locale === 'ar' ? 'الأحد - الخميس: 8:00 - 16:00' : 'Sunday - Thursday: 8:00 - 16:00'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: t('legal')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 mb-6\",\n                                    children: legalLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: link.href,\n                                                className: \"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm\",\n                                                children: t(link.key)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, link.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: `text-sm font-semibold text-pure-white mb-3 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                            children: t('socialMedia')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3 rtl:space-x-reverse\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\",\n                                                    \"aria-label\": \"Facebook\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-pure-white font-bold\",\n                                                        children: \"f\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\",\n                                                    \"aria-label\": \"Twitter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-pure-white font-bold\",\n                                                        children: \"\\uD835\\uDD4F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"w-8 h-8 bg-steel-gray hover:bg-military-green rounded-full flex items-center justify-center transition-colors duration-200\",\n                                                    \"aria-label\": \"YouTube\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-pure-white font-bold\",\n                                                        children: \"▶\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-steel-gray/30 mt-8 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-muted-gray text-sm ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                children: t('copyright')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-muted-gray text-xs ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                    children: locale === 'ar' ? 'آخر تحديث: ديسمبر 2024' : 'Last Updated: December 2024'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/layout/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,LanguageIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/LanguageIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,LanguageIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,LanguageIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,LanguageIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navigation() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_4__.useTranslations)('navigation');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useLocale)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLanguageOpen, setIsLanguageOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigationItems = [\n        {\n            key: 'home',\n            href: `/${locale}`\n        },\n        {\n            key: 'about',\n            href: `/${locale}/about`\n        },\n        {\n            key: 'structure',\n            href: `/${locale}/structure`\n        },\n        {\n            key: 'news',\n            href: `/${locale}/news`\n        },\n        {\n            key: 'projects',\n            href: `/${locale}/projects`\n        },\n        {\n            key: 'media',\n            href: `/${locale}/media`\n        },\n        {\n            key: 'contact',\n            href: `/${locale}/contact`\n        }\n    ];\n    const switchLanguage = (newLocale)=>{\n        const currentPath = pathname.replace(`/${locale}`, '');\n        router.push(`/${newLocale}${currentPath}`);\n        setIsLanguageOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-military-black/90 backdrop-blur-md border-b border-steel-gray/30\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: `/${locale}`,\n                            className: \"flex items-center space-x-3 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-military-green rounded-full flex items-center justify-center military-glow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-pure-white font-bold text-lg\",\n                                        children: \"ود\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-pure-white\",\n                                        children: locale === 'ar' ? 'وزارة الدفاع السورية' : 'Syrian Ministry of Defense'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8 rtl:space-x-reverse\",\n                            children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: item.href,\n                                    className: \"text-light-gray hover:text-bright-green transition-colors duration-200 text-sm font-medium\",\n                                    children: t(item.key)\n                                }, item.key, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 rtl:space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsLanguageOpen(!isLanguageOpen),\n                                            className: \"flex items-center space-x-2 rtl:space-x-reverse text-light-gray hover:text-bright-green transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: locale.toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                                            children: isLanguageOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: -10\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    y: -10\n                                                },\n                                                className: \"absolute top-full mt-2 right-0 bg-steel-gray rounded-lg shadow-lg border border-smoke-gray/30 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>switchLanguage('ar'),\n                                                        className: \"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200\",\n                                                        children: \"العربية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>switchLanguage('en'),\n                                                        className: \"block w-full px-4 py-2 text-left text-sm text-light-gray hover:bg-smoke-gray hover:text-bright-green transition-colors duration-200\",\n                                                        children: \"English\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                    className: \"lg:hidden text-light-gray hover:text-bright-green transition-colors duration-200\",\n                                    children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_LanguageIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: 'auto'\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    className: \"lg:hidden bg-steel-gray/95 backdrop-blur-md border-t border-smoke-gray/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-4 space-y-2\",\n                        children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: item.href,\n                                onClick: ()=>setIsMenuOpen(false),\n                                className: \"block px-4 py-2 text-light-gray hover:text-bright-green hover:bg-smoke-gray/30 rounded-lg transition-all duration-200\",\n                                children: t(item.key)\n                            }, item.key, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sections/HistoricalTimeline.tsx":
/*!****************************************************!*\
  !*** ./components/sections/HistoricalTimeline.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HistoricalTimeline)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,StarIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HistoricalTimeline() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)('about.timeline');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const [selectedEvent, setSelectedEvent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useInView)(containerRef, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const events = t.raw('events');\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 50,\n            scale: 0.9\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1\n        }\n    };\n    const lineVariants = {\n        hidden: {\n            scaleY: 0\n        },\n        visible: {\n            scaleY: 1\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-deep-charcoal camo-pattern\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.h2, {\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6\n                            },\n                            className: `text-3xl md:text-4xl font-bold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: `text-light-gray text-lg ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                            children: t('subtitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    ref: containerRef,\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: lineVariants,\n                            initial: \"hidden\",\n                            animate: isInView ? \"visible\" : \"hidden\",\n                            transition: {\n                                duration: 1.5,\n                                ease: \"easeInOut\",\n                                delay: 0.3\n                            },\n                            className: \"absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-military-green via-bright-green to-military-green h-full origin-top\",\n                            style: {\n                                background: 'linear-gradient(to bottom, #2d5016, #4a7c59, #2d5016)',\n                                boxShadow: '0 0 20px rgba(45, 80, 22, 0.5)'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: containerVariants,\n                            initial: \"hidden\",\n                            animate: isInView ? \"visible\" : \"hidden\",\n                            className: \"space-y-12\",\n                            children: events.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    variants: itemVariants,\n                                    transition: {\n                                        duration: 0.6,\n                                        ease: \"easeOut\"\n                                    },\n                                    className: `relative flex items-center ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.2\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            className: \"absolute left-1/2 transform -translate-x-1/2 z-10 cursor-pointer\",\n                                            onClick: ()=>setSelectedEvent(selectedEvent === index ? null : index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-military-green rounded-full border-4 border-pure-white military-glow-strong flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                    animate: {\n                                                        scale: selectedEvent === index ? [\n                                                            1,\n                                                            1.5,\n                                                            1\n                                                        ] : 1,\n                                                        rotate: selectedEvent === index ? [\n                                                            0,\n                                                            180,\n                                                            360\n                                                        ] : 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3 text-pure-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.02,\n                                                y: -5\n                                            },\n                                            className: `w-full max-w-md ${index % 2 === 0 ? 'mr-auto pr-8' : 'ml-auto pl-8'}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-steel-gray/70 backdrop-blur-sm rounded-lg p-6 border border-smoke-gray/30 military-glow hover:military-glow-strong transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex items-center mb-3 ${index % 2 === 0 ? 'justify-start' : 'justify-end'}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-military-green text-pure-white px-4 py-2 rounded-full flex items-center space-x-2 rtl:space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_StarIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-lg\",\n                                                                    children: event.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: `text-xl font-bold text-bright-green mb-3 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'} ${index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'}`,\n                                                        children: event.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-light-gray leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'} ${index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'}`,\n                                                        children: event.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        onClick: ()=>setSelectedEvent(selectedEvent === index ? null : index),\n                                                        className: `mt-4 text-military-green hover:text-bright-green font-medium text-sm transition-colors duration-200 ${index % 2 === 0 ? 'text-left rtl:text-right' : 'text-right rtl:text-left'}`,\n                                                        children: selectedEvent === index ? locale === 'ar' ? 'إخفاء التفاصيل' : 'Hide Details' : locale === 'ar' ? 'عرض التفاصيل' : 'Show Details'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        initial: false,\n                                                        animate: {\n                                                            height: selectedEvent === index ? 'auto' : 0,\n                                                            opacity: selectedEvent === index ? 1 : 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.3\n                                                        },\n                                                        className: \"overflow-hidden\",\n                                                        children: selectedEvent === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 pt-4 border-t border-smoke-gray/30\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-military-black/30 rounded-lg p-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: `text-light-gray text-sm leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                                                    children: locale === 'ar' ? 'هذا الحدث يمثل محطة مهمة في تاريخ وزارة الدفاع السورية، حيث ساهم في تعزيز قدراتها الدفاعية وترسيخ دورها في حماية الوطن والمواطن.' : 'This event represents an important milestone in the history of the Syrian Ministry of Defense, contributing to strengthening its defensive capabilities and consolidating its role in protecting the nation and citizens.'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                            initial: {\n                                                scaleX: 0\n                                            },\n                                            animate: isInView ? {\n                                                scaleX: 1\n                                            } : {},\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.5 + index * 0.1\n                                            },\n                                            className: `absolute top-1/2 w-8 h-0.5 bg-military-green origin-${index % 2 === 0 ? 'right' : 'left'} ${index % 2 === 0 ? 'left-1/2 ml-3' : 'right-1/2 mr-3'}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                scale: 1\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: 1.5\n                            },\n                            className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-gradient-to-br from-military-green to-bright-green rounded-full border-4 border-pure-white military-glow-strong flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-pure-white rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.6,\n                        delay: 1.8\n                    },\n                    className: \"text-center mt-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: `text-light-gray text-lg mb-6 ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                            children: locale === 'ar' ? 'تاريخ حافل بالإنجازات والتضحيات في سبيل الوطن' : 'A rich history of achievements and sacrifices for the homeland'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.button, {\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            whileTap: {\n                                scale: 0.95\n                            },\n                            className: \"bg-military-green hover:bg-bright-green text-pure-white px-8 py-3 rounded-lg font-semibold transition-all duration-200 military-glow\",\n                            children: locale === 'ar' ? 'اعرف المزيد عن تاريخنا' : 'Learn More About Our History'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\HistoricalTimeline.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sections/HistoricalTimeline.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sections/LeadershipCarousel.tsx":
/*!****************************************************!*\
  !*** ./components/sections/LeadershipCarousel.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LeadershipCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeftIcon,ChevronRightIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction LeadershipCarousel() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)('about.leadership');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Mock leadership data - in real implementation, this would come from API/CMS\n    const leaders = [\n        {\n            id: '1',\n            position: t('positions.minister'),\n            name: locale === 'ar' ? 'الفريق علي محمود عباس' : 'General Ali Mahmoud Abbas',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'وزير الدفاع، خبرة 30 عاماً في الخدمة العسكرية والقيادة الاستراتيجية' : 'Minister of Defense, 30 years of experience in military service and strategic leadership'\n        },\n        {\n            id: '2',\n            position: t('positions.deputyMinister'),\n            name: locale === 'ar' ? 'اللواء أحمد سعيد الخوري' : 'Major General Ahmad Said Al-Khoury',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'نائب وزير الدفاع، متخصص في التطوير التقني والتحديث العسكري' : 'Deputy Minister of Defense, specialist in technical development and military modernization'\n        },\n        {\n            id: '3',\n            position: t('positions.chiefOfStaff'),\n            name: locale === 'ar' ? 'الفريق محمد حسن النوري' : 'General Mohammad Hassan Al-Nouri',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'رئيس الأركان العامة، قائد عسكري محنك بخبرة 25 عاماً' : 'Chief of General Staff, seasoned military commander with 25 years of experience'\n        },\n        {\n            id: '4',\n            position: t('positions.deputyChiefOfStaff'),\n            name: locale === 'ar' ? 'اللواء خالد عمر الشامي' : 'Major General Khaled Omar Al-Shami',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'نائب رئيس الأركان، خبير في العمليات العسكرية والتخطيط الاستراتيجي' : 'Deputy Chief of Staff, expert in military operations and strategic planning'\n        },\n        {\n            id: '5',\n            position: t('positions.operationsDirector'),\n            name: locale === 'ar' ? 'العميد يوسف طارق الحلبي' : 'Brigadier General Youssef Tarek Al-Halabi',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'مدير العمليات، متخصص في التكتيكات العسكرية الحديثة' : 'Director of Operations, specialist in modern military tactics'\n        },\n        {\n            id: '6',\n            position: t('positions.intelligenceDirector'),\n            name: locale === 'ar' ? 'العميد سامر فؤاد الدمشقي' : 'Brigadier General Samer Fouad Al-Dimashqi',\n            image: '/api/placeholder/300/400',\n            description: locale === 'ar' ? 'مدير المخابرات العسكرية، خبير في الأمن القومي والاستخبارات' : 'Director of Military Intelligence, expert in national security and intelligence'\n        }\n    ];\n    // Auto-play functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LeadershipCarousel.useEffect\": ()=>{\n            if (!isAutoPlaying) return;\n            const interval = setInterval({\n                \"LeadershipCarousel.useEffect.interval\": ()=>{\n                    setCurrentIndex({\n                        \"LeadershipCarousel.useEffect.interval\": (prev)=>(prev + 1) % leaders.length\n                    }[\"LeadershipCarousel.useEffect.interval\"]);\n                }\n            }[\"LeadershipCarousel.useEffect.interval\"], 5000);\n            return ({\n                \"LeadershipCarousel.useEffect\": ()=>clearInterval(interval)\n            })[\"LeadershipCarousel.useEffect\"];\n        }\n    }[\"LeadershipCarousel.useEffect\"], [\n        isAutoPlaying,\n        leaders.length\n    ]);\n    const nextSlide = ()=>{\n        setCurrentIndex((prev)=>(prev + 1) % leaders.length);\n        setIsAutoPlaying(false);\n    };\n    const prevSlide = ()=>{\n        setCurrentIndex((prev)=>(prev - 1 + leaders.length) % leaders.length);\n        setIsAutoPlaying(false);\n    };\n    const goToSlide = (index)=>{\n        setCurrentIndex(index);\n        setIsAutoPlaying(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-steel-gray/30 backdrop-blur-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: `text-3xl md:text-4xl font-bold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                            children: t('title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: `text-light-gray text-lg ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                            children: t('subtitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative overflow-hidden rounded-lg bg-military-black/50 backdrop-blur-sm border border-smoke-gray/30 military-glow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                                    mode: \"wait\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: locale === 'ar' ? -100 : 100\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            x: locale === 'ar' ? 100 : -100\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            ease: \"easeInOut\"\n                                        },\n                                        className: \"flex flex-col lg:flex-row items-center p-8 lg:p-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mb-8 lg:mb-0 lg:mr-8 rtl:lg:mr-0 rtl:lg:ml-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-48 h-64 md:w-56 md:h-72 bg-smoke-gray rounded-lg overflow-hidden military-glow\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full bg-gradient-to-br from-steel-gray to-smoke-gray flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-military-green rounded-full flex items-center justify-center mb-4 mx-auto\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-2xl font-bold text-pure-white\",\n                                                                                children: leaders[currentIndex].name.charAt(0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                                                lineNumber: 143,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                                            lineNumber: 142,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-light-gray text-sm\",\n                                                                            children: locale === 'ar' ? 'صورة رسمية' : 'Official Photo'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                                            lineNumber: 147,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                                    lineNumber: 141,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 -right-2 rtl:-left-2 rtl:-right-auto bg-military-green text-pure-white px-3 py-1 rounded-full text-xs font-semibold\",\n                                                            children: [\n                                                                currentIndex + 1,\n                                                                \"/\",\n                                                                leaders.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-center lg:text-left rtl:lg:text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: `text-2xl md:text-3xl font-bold text-bright-green mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                                        children: leaders[currentIndex].position\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: `text-xl md:text-2xl font-semibold text-pure-white mb-4 ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                                        children: leaders[currentIndex].name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-light-gray leading-relaxed text-lg ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                                        children: leaders[currentIndex].description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, currentIndex, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevSlide,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-military-black/70 hover:bg-military-green/70 text-pure-white p-2 rounded-full transition-all duration-200 military-glow\",\n                                    \"aria-label\": locale === 'ar' ? 'السابق' : 'Previous',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextSlide,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-military-black/70 hover:bg-military-green/70 text-pure-white p-2 rounded-full transition-all duration-200 military-glow\",\n                                    \"aria-label\": locale === 'ar' ? 'التالي' : 'Next',\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeftIcon_ChevronRightIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-6 space-x-2 rtl:space-x-reverse\",\n                            children: leaders.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>goToSlide(index),\n                                    className: `w-3 h-3 rounded-full transition-all duration-200 ${index === currentIndex ? 'bg-military-green military-glow' : 'bg-smoke-gray hover:bg-bright-green/50'}`,\n                                    \"aria-label\": `${locale === 'ar' ? 'انتقل إلى الشريحة' : 'Go to slide'} ${index + 1}`\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsAutoPlaying(!isAutoPlaying),\n                                className: `px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${isAutoPlaying ? 'bg-military-green text-pure-white' : 'bg-smoke-gray text-light-gray hover:bg-bright-green/50'}`,\n                                children: isAutoPlaying ? locale === 'ar' ? 'إيقاف التشغيل التلقائي' : 'Pause Auto-play' : locale === 'ar' ? 'تشغيل تلقائي' : 'Auto-play'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\LeadershipCarousel.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3NlY3Rpb25zL0xlYWRlcnNoaXBDYXJvdXNlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1c7QUFDQztBQUN3QjtBQVVqRSxTQUFTUTtJQUN0QixNQUFNQyxJQUFJUCwwREFBZUEsQ0FBQztJQUMxQixNQUFNUSxTQUFTUCxvREFBU0E7SUFDeEIsTUFBTSxDQUFDUSxjQUFjQyxnQkFBZ0IsR0FBR1osK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDYSxlQUFlQyxpQkFBaUIsR0FBR2QsK0NBQVFBLENBQUM7SUFFbkQsOEVBQThFO0lBQzlFLE1BQU1lLFVBQW9CO1FBQ3hCO1lBQ0VDLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sMEJBQTBCO1lBQ2xEUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsd0VBQ0E7UUFDTjtRQUNBO1lBQ0VNLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sNEJBQTRCO1lBQ3BEUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsK0RBQ0E7UUFDTjtRQUNBO1lBQ0VNLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sMkJBQTJCO1lBQ25EUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsd0RBQ0E7UUFDTjtRQUNBO1lBQ0VNLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sMkJBQTJCO1lBQ25EUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsc0VBQ0E7UUFDTjtRQUNBO1lBQ0VNLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sNEJBQTRCO1lBQ3BEUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsdURBQ0E7UUFDTjtRQUNBO1lBQ0VNLElBQUk7WUFDSkMsVUFBVVIsRUFBRTtZQUNaUyxNQUFNUixXQUFXLE9BQU8sNkJBQTZCO1lBQ3JEUyxPQUFPO1lBQ1BDLGFBQWFWLFdBQVcsT0FDcEIsK0RBQ0E7UUFDTjtLQUNEO0lBRUQsMEJBQTBCO0lBQzFCVCxnREFBU0E7d0NBQUM7WUFDUixJQUFJLENBQUNZLGVBQWU7WUFFcEIsTUFBTVEsV0FBV0M7eURBQVk7b0JBQzNCVjtpRUFBZ0IsQ0FBQ1csT0FBUyxDQUFDQSxPQUFPLEtBQUtSLFFBQVFTLE1BQU07O2dCQUN2RDt3REFBRztZQUVIO2dEQUFPLElBQU1DLGNBQWNKOztRQUM3Qjt1Q0FBRztRQUFDUjtRQUFlRSxRQUFRUyxNQUFNO0tBQUM7SUFFbEMsTUFBTUUsWUFBWTtRQUNoQmQsZ0JBQWdCLENBQUNXLE9BQVMsQ0FBQ0EsT0FBTyxLQUFLUixRQUFRUyxNQUFNO1FBQ3JEVixpQkFBaUI7SUFDbkI7SUFFQSxNQUFNYSxZQUFZO1FBQ2hCZixnQkFBZ0IsQ0FBQ1csT0FBUyxDQUFDQSxPQUFPLElBQUlSLFFBQVFTLE1BQU0sSUFBSVQsUUFBUVMsTUFBTTtRQUN0RVYsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTWMsWUFBWSxDQUFDQztRQUNqQmpCLGdCQUFnQmlCO1FBQ2hCZixpQkFBaUI7SUFDbkI7SUFFQSxxQkFDRSw4REFBQ2dCO1FBQVFDLFdBQVU7a0JBQ2pCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFFYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDRTs0QkFBR0YsV0FBVyxDQUFDLG9EQUFvRCxFQUNsRXJCLFdBQVcsT0FBTyx5QkFBeUIsd0JBQzNDO3NDQUNDRCxFQUFFOzs7Ozs7c0NBRUwsOERBQUN5Qjs0QkFBRUgsV0FBVyxDQUFDLHdCQUF3QixFQUNyQ3JCLFdBQVcsT0FBTywwQkFBMEIsMEJBQzVDO3NDQUNDRCxFQUFFOzs7Ozs7Ozs7Ozs7OEJBS1AsOERBQUN1QjtvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQzFCLDBEQUFlQTtvQ0FBQzhCLE1BQUs7OENBQ3BCLDRFQUFDL0IsaURBQU1BLENBQUM0QixHQUFHO3dDQUVUSSxTQUFTOzRDQUFFQyxTQUFTOzRDQUFHQyxHQUFHNUIsV0FBVyxPQUFPLENBQUMsTUFBTTt3Q0FBSTt3Q0FDdkQ2QixTQUFTOzRDQUFFRixTQUFTOzRDQUFHQyxHQUFHO3dDQUFFO3dDQUM1QkUsTUFBTTs0Q0FBRUgsU0FBUzs0Q0FBR0MsR0FBRzVCLFdBQVcsT0FBTyxNQUFNLENBQUM7d0NBQUk7d0NBQ3BEK0IsWUFBWTs0Q0FBRUMsVUFBVTs0Q0FBS0MsTUFBTTt3Q0FBWTt3Q0FDL0NaLFdBQVU7OzBEQUdWLDhEQUFDQztnREFBSUQsV0FBVTswREFDYiw0RUFBQ0M7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBSUQsV0FBVTtzRUFDYiw0RUFBQ0M7Z0VBQUlELFdBQVU7MEVBQ2IsNEVBQUNDO29FQUFJRCxXQUFVOztzRkFDYiw4REFBQ0M7NEVBQUlELFdBQVU7c0ZBQ2IsNEVBQUNhO2dGQUFLYixXQUFVOzBGQUNiaEIsT0FBTyxDQUFDSixhQUFhLENBQUNPLElBQUksQ0FBQzJCLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7c0ZBR3ZDLDhEQUFDWDs0RUFBRUgsV0FBVTtzRkFDVnJCLFdBQVcsT0FBTyxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQU8xQyw4REFBQ3NCOzREQUFJRCxXQUFVOztnRUFDWnBCLGVBQWU7Z0VBQUU7Z0VBQUVJLFFBQVFTLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNeEMsOERBQUNRO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ2U7d0RBQUdmLFdBQVcsQ0FBQyxzREFBc0QsRUFDcEVyQixXQUFXLE9BQU8seUJBQXlCLHdCQUMzQztrRUFDQ0ssT0FBTyxDQUFDSixhQUFhLENBQUNNLFFBQVE7Ozs7OztrRUFFakMsOERBQUM4Qjt3REFBR2hCLFdBQVcsQ0FBQyx1REFBdUQsRUFDckVyQixXQUFXLE9BQU8sMEJBQTBCLDBCQUM1QztrRUFDQ0ssT0FBTyxDQUFDSixhQUFhLENBQUNPLElBQUk7Ozs7OztrRUFFN0IsOERBQUNnQjt3REFBRUgsV0FBVyxDQUFDLHdDQUF3QyxFQUNyRHJCLFdBQVcsT0FBTywwQkFBMEIsMEJBQzVDO2tFQUNDSyxPQUFPLENBQUNKLGFBQWEsQ0FBQ1MsV0FBVzs7Ozs7Ozs7Ozs7Ozt1Q0EvQ2pDVDs7Ozs7Ozs7Ozs4Q0FzRFQsOERBQUNxQztvQ0FDQ0MsU0FBU3RCO29DQUNUSSxXQUFVO29DQUNWbUIsY0FBWXhDLFdBQVcsT0FBTyxXQUFXOzhDQUV6Qyw0RUFBQ0osMEhBQWVBO3dDQUFDeUIsV0FBVTs7Ozs7Ozs7Ozs7OENBRTdCLDhEQUFDaUI7b0NBQ0NDLFNBQVN2QjtvQ0FDVEssV0FBVTtvQ0FDVm1CLGNBQVl4QyxXQUFXLE9BQU8sV0FBVzs4Q0FFekMsNEVBQUNILDBIQUFnQkE7d0NBQUN3QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLaEMsOERBQUNDOzRCQUFJRCxXQUFVO3NDQUNaaEIsUUFBUW9DLEdBQUcsQ0FBQyxDQUFDQyxHQUFHdkIsc0JBQ2YsOERBQUNtQjtvQ0FFQ0MsU0FBUyxJQUFNckIsVUFBVUM7b0NBQ3pCRSxXQUFXLENBQUMsaURBQWlELEVBQzNERixVQUFVbEIsZUFDTixvQ0FDQSwwQ0FDSjtvQ0FDRnVDLGNBQVksR0FBR3hDLFdBQVcsT0FBTyxzQkFBc0IsY0FBYyxDQUFDLEVBQUVtQixRQUFRLEdBQUc7bUNBUDlFQTs7Ozs7Ozs7OztzQ0FhWCw4REFBQ0c7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNpQjtnQ0FDQ0MsU0FBUyxJQUFNbkMsaUJBQWlCLENBQUNEO2dDQUNqQ2tCLFdBQVcsQ0FBQyxxRUFBcUUsRUFDL0VsQixnQkFDSSxzQ0FDQSwwREFDSjswQ0FFREEsZ0JBQ0lILFdBQVcsT0FBTywyQkFBMkIsb0JBQzdDQSxXQUFXLE9BQU8saUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXREIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFtZXJrXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXGRlZmVuY2UgbWluaXN0ZXIgb2Ygc3lyaWFcXHN5cmlhbi1kZWZlbnNlLW1pbmlzdHJ5XFxjb21wb25lbnRzXFxzZWN0aW9uc1xcTGVhZGVyc2hpcENhcm91c2VsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbnMsIHVzZUxvY2FsZSB9IGZyb20gJ25leHQtaW50bCc7XG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xuaW1wb3J0IHsgQ2hldnJvbkxlZnRJY29uLCBDaGV2cm9uUmlnaHRJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIExlYWRlciB7XG4gIGlkOiBzdHJpbmc7XG4gIHBvc2l0aW9uOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgaW1hZ2U6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGVhZGVyc2hpcENhcm91c2VsKCkge1xuICBjb25zdCB0ID0gdXNlVHJhbnNsYXRpb25zKCdhYm91dC5sZWFkZXJzaGlwJyk7XG4gIGNvbnN0IGxvY2FsZSA9IHVzZUxvY2FsZSgpO1xuICBjb25zdCBbY3VycmVudEluZGV4LCBzZXRDdXJyZW50SW5kZXhdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtpc0F1dG9QbGF5aW5nLCBzZXRJc0F1dG9QbGF5aW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vIE1vY2sgbGVhZGVyc2hpcCBkYXRhIC0gaW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdGhpcyB3b3VsZCBjb21lIGZyb20gQVBJL0NNU1xuICBjb25zdCBsZWFkZXJzOiBMZWFkZXJbXSA9IFtcbiAgICB7XG4gICAgICBpZDogJzEnLFxuICAgICAgcG9zaXRpb246IHQoJ3Bvc2l0aW9ucy5taW5pc3RlcicpLFxuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9in2YTZgdix2YrZgiDYudmE2Yog2YXYrdmF2YjYryDYudio2KfYsycgOiAnR2VuZXJhbCBBbGkgTWFobW91ZCBBYmJhcycsXG4gICAgICBpbWFnZTogJy9hcGkvcGxhY2Vob2xkZXIvMzAwLzQwMCcsXG4gICAgICBkZXNjcmlwdGlvbjogbG9jYWxlID09PSAnYXInIFxuICAgICAgICA/ICfZiNiy2YrYsSDYp9mE2K/Zgdin2LnYjCDYrtio2LHYqSAzMCDYudin2YXYp9mLINmB2Yog2KfZhNiu2K/ZhdipINin2YTYudiz2YPYsdmK2Kkg2YjYp9mE2YLZitin2K/YqSDYp9mE2KfYs9iq2LHYp9iq2YrYrNmK2KknXG4gICAgICAgIDogJ01pbmlzdGVyIG9mIERlZmVuc2UsIDMwIHllYXJzIG9mIGV4cGVyaWVuY2UgaW4gbWlsaXRhcnkgc2VydmljZSBhbmQgc3RyYXRlZ2ljIGxlYWRlcnNoaXAnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzInLFxuICAgICAgcG9zaXRpb246IHQoJ3Bvc2l0aW9ucy5kZXB1dHlNaW5pc3RlcicpLFxuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9in2YTZhNmI2KfYoSDYo9it2YXYryDYs9i52YrYryDYp9mE2K7ZiNix2YonIDogJ01ham9yIEdlbmVyYWwgQWhtYWQgU2FpZCBBbC1LaG91cnknLFxuICAgICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzMwMC80MDAnLFxuICAgICAgZGVzY3JpcHRpb246IGxvY2FsZSA9PT0gJ2FyJ1xuICAgICAgICA/ICfZhtin2KbYqCDZiNiy2YrYsSDYp9mE2K/Zgdin2LnYjCDZhdiq2K7Ytdi1INmB2Yog2KfZhNiq2LfZiNmK2LEg2KfZhNiq2YLZhtmKINmI2KfZhNiq2K3Yr9mK2Ksg2KfZhNi52LPZg9ix2YonXG4gICAgICAgIDogJ0RlcHV0eSBNaW5pc3RlciBvZiBEZWZlbnNlLCBzcGVjaWFsaXN0IGluIHRlY2huaWNhbCBkZXZlbG9wbWVudCBhbmQgbWlsaXRhcnkgbW9kZXJuaXphdGlvbidcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMycsXG4gICAgICBwb3NpdGlvbjogdCgncG9zaXRpb25zLmNoaWVmT2ZTdGFmZicpLFxuICAgICAgbmFtZTogbG9jYWxlID09PSAnYXInID8gJ9in2YTZgdix2YrZgiDZhdit2YXYryDYrdiz2YYg2KfZhNmG2YjYsdmKJyA6ICdHZW5lcmFsIE1vaGFtbWFkIEhhc3NhbiBBbC1Ob3VyaScsXG4gICAgICBpbWFnZTogJy9hcGkvcGxhY2Vob2xkZXIvMzAwLzQwMCcsXG4gICAgICBkZXNjcmlwdGlvbjogbG9jYWxlID09PSAnYXInXG4gICAgICAgID8gJ9ix2KbZitizINin2YTYo9ix2YPYp9mGINin2YTYudin2YXYqdiMINmC2KfYptivINi52LPZg9ix2Yog2YXYrdmG2YMg2KjYrtio2LHYqSAyNSDYudin2YXYp9mLJ1xuICAgICAgICA6ICdDaGllZiBvZiBHZW5lcmFsIFN0YWZmLCBzZWFzb25lZCBtaWxpdGFyeSBjb21tYW5kZXIgd2l0aCAyNSB5ZWFycyBvZiBleHBlcmllbmNlJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc0JyxcbiAgICAgIHBvc2l0aW9uOiB0KCdwb3NpdGlvbnMuZGVwdXR5Q2hpZWZPZlN0YWZmJyksXG4gICAgICBuYW1lOiBsb2NhbGUgPT09ICdhcicgPyAn2KfZhNmE2YjYp9ihINiu2KfZhNivINi52YXYsSDYp9mE2LTYp9mF2YonIDogJ01ham9yIEdlbmVyYWwgS2hhbGVkIE9tYXIgQWwtU2hhbWknLFxuICAgICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzMwMC80MDAnLFxuICAgICAgZGVzY3JpcHRpb246IGxvY2FsZSA9PT0gJ2FyJ1xuICAgICAgICA/ICfZhtin2KbYqCDYsdim2YrYsyDYp9mE2KPYsdmD2KfZhtiMINiu2KjZitixINmB2Yog2KfZhNi52YXZhNmK2KfYqiDYp9mE2LnYs9mD2LHZitipINmI2KfZhNiq2K7Yt9mK2Lcg2KfZhNin2LPYqtix2KfYqtmK2KzZiidcbiAgICAgICAgOiAnRGVwdXR5IENoaWVmIG9mIFN0YWZmLCBleHBlcnQgaW4gbWlsaXRhcnkgb3BlcmF0aW9ucyBhbmQgc3RyYXRlZ2ljIHBsYW5uaW5nJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc1JyxcbiAgICAgIHBvc2l0aW9uOiB0KCdwb3NpdGlvbnMub3BlcmF0aW9uc0RpcmVjdG9yJyksXG4gICAgICBuYW1lOiBsb2NhbGUgPT09ICdhcicgPyAn2KfZhNi52YXZitivINmK2YjYs9mBINi32KfYsdmCINin2YTYrdmE2KjZiicgOiAnQnJpZ2FkaWVyIEdlbmVyYWwgWW91c3NlZiBUYXJlayBBbC1IYWxhYmknLFxuICAgICAgaW1hZ2U6ICcvYXBpL3BsYWNlaG9sZGVyLzMwMC80MDAnLFxuICAgICAgZGVzY3JpcHRpb246IGxvY2FsZSA9PT0gJ2FyJ1xuICAgICAgICA/ICfZhdiv2YrYsSDYp9mE2LnZhdmE2YrYp9iq2Iwg2YXYqtiu2LXYtSDZgdmKINin2YTYqtmD2KrZitmD2KfYqiDYp9mE2LnYs9mD2LHZitipINin2YTYrdiv2YrYq9ipJ1xuICAgICAgICA6ICdEaXJlY3RvciBvZiBPcGVyYXRpb25zLCBzcGVjaWFsaXN0IGluIG1vZGVybiBtaWxpdGFyeSB0YWN0aWNzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICc2JyxcbiAgICAgIHBvc2l0aW9uOiB0KCdwb3NpdGlvbnMuaW50ZWxsaWdlbmNlRGlyZWN0b3InKSxcbiAgICAgIG5hbWU6IGxvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2LnZhdmK2K8g2LPYp9mF2LEg2YHYpNin2K8g2KfZhNiv2YXYtNmC2YonIDogJ0JyaWdhZGllciBHZW5lcmFsIFNhbWVyIEZvdWFkIEFsLURpbWFzaHFpJyxcbiAgICAgIGltYWdlOiAnL2FwaS9wbGFjZWhvbGRlci8zMDAvNDAwJyxcbiAgICAgIGRlc2NyaXB0aW9uOiBsb2NhbGUgPT09ICdhcidcbiAgICAgICAgPyAn2YXYr9mK2LEg2KfZhNmF2K7Yp9io2LHYp9iqINin2YTYudiz2YPYsdmK2KnYjCDYrtio2YrYsSDZgdmKINin2YTYo9mF2YYg2KfZhNmC2YjZhdmKINmI2KfZhNin2LPYqtiu2KjYp9ix2KfYqidcbiAgICAgICAgOiAnRGlyZWN0b3Igb2YgTWlsaXRhcnkgSW50ZWxsaWdlbmNlLCBleHBlcnQgaW4gbmF0aW9uYWwgc2VjdXJpdHkgYW5kIGludGVsbGlnZW5jZSdcbiAgICB9XG4gIF07XG5cbiAgLy8gQXV0by1wbGF5IGZ1bmN0aW9uYWxpdHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzQXV0b1BsYXlpbmcpIHJldHVybjtcbiAgICBcbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldEN1cnJlbnRJbmRleCgocHJldikgPT4gKHByZXYgKyAxKSAlIGxlYWRlcnMubGVuZ3RoKTtcbiAgICB9LCA1MDAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW2lzQXV0b1BsYXlpbmcsIGxlYWRlcnMubGVuZ3RoXSk7XG5cbiAgY29uc3QgbmV4dFNsaWRlID0gKCkgPT4ge1xuICAgIHNldEN1cnJlbnRJbmRleCgocHJldikgPT4gKHByZXYgKyAxKSAlIGxlYWRlcnMubGVuZ3RoKTtcbiAgICBzZXRJc0F1dG9QbGF5aW5nKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBwcmV2U2xpZGUgPSAoKSA9PiB7XG4gICAgc2V0Q3VycmVudEluZGV4KChwcmV2KSA9PiAocHJldiAtIDEgKyBsZWFkZXJzLmxlbmd0aCkgJSBsZWFkZXJzLmxlbmd0aCk7XG4gICAgc2V0SXNBdXRvUGxheWluZyhmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgZ29Ub1NsaWRlID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBzZXRDdXJyZW50SW5kZXgoaW5kZXgpO1xuICAgIHNldElzQXV0b1BsYXlpbmcoZmFsc2UpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktMTYgYmctc3RlZWwtZ3JheS8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgIHsvKiBTZWN0aW9uIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9e2B0ZXh0LTN4bCBtZDp0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1wdXJlLXdoaXRlIG1iLTQgJHtcbiAgICAgICAgICAgIGxvY2FsZSA9PT0gJ2FyJyA/ICdmb250LWFyYWJpYy1oZWFkaW5ncycgOiAnZm9udC1lbmdsaXNoLXByaW1hcnknXG4gICAgICAgICAgfWB9PlxuICAgICAgICAgICAge3QoJ3RpdGxlJyl9XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LWxpZ2h0LWdyYXkgdGV4dC1sZyAke1xuICAgICAgICAgICAgbG9jYWxlID09PSAnYXInID8gJ2ZvbnQtYXJhYmljLXNlY29uZGFyeScgOiAnZm9udC1lbmdsaXNoLXNlY29uZGFyeSdcbiAgICAgICAgICB9YH0+XG4gICAgICAgICAgICB7dCgnc3VidGl0bGUnKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDYXJvdXNlbCBDb250YWluZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICB7LyogTWFpbiBDYXJvdXNlbCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG92ZXJmbG93LWhpZGRlbiByb3VuZGVkLWxnIGJnLW1pbGl0YXJ5LWJsYWNrLzUwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1zbW9rZS1ncmF5LzMwIG1pbGl0YXJ5LWdsb3dcIj5cbiAgICAgICAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIj5cbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9e2N1cnJlbnRJbmRleH1cbiAgICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHg6IGxvY2FsZSA9PT0gJ2FyJyA/IC0xMDAgOiAxMDAgfX1cbiAgICAgICAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgICAgICBleGl0PXt7IG9wYWNpdHk6IDAsIHg6IGxvY2FsZSA9PT0gJ2FyJyA/IDEwMCA6IC0xMDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGVhc2U6IFwiZWFzZUluT3V0XCIgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGl0ZW1zLWNlbnRlciBwLTggbGc6cC0xMlwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7LyogTGVhZGVyIEltYWdlICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBtYi04IGxnOm1iLTAgbGc6bXItOCBydGw6bGc6bXItMCBydGw6bGc6bWwtOFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNDggaC02NCBtZDp3LTU2IG1kOmgtNzIgYmctc21va2UtZ3JheSByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBtaWxpdGFyeS1nbG93XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc3RlZWwtZ3JheSB0by1zbW9rZS1ncmF5IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLW1pbGl0YXJ5LWdyZWVuIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00IG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJlLXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bGVhZGVyc1tjdXJyZW50SW5kZXhdLm5hbWUuY2hhckF0KDApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGlnaHQtZ3JheSB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2xvY2FsZSA9PT0gJ2FyJyA/ICfYtdmI2LHYqSDYsdiz2YXZitipJyA6ICdPZmZpY2lhbCBQaG90byd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHsvKiBSYW5rIEluZGljYXRvciAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTIgLXJpZ2h0LTIgcnRsOi1sZWZ0LTIgcnRsOi1yaWdodC1hdXRvIGJnLW1pbGl0YXJ5LWdyZWVuIHRleHQtcHVyZS13aGl0ZSBweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1zZW1pYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50SW5kZXggKyAxfS97bGVhZGVycy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogTGVhZGVyIEluZm8gKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1jZW50ZXIgbGc6dGV4dC1sZWZ0IHJ0bDpsZzp0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPXtgdGV4dC0yeGwgbWQ6dGV4dC0zeGwgZm9udC1ib2xkIHRleHQtYnJpZ2h0LWdyZWVuIG1iLTIgJHtcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxlID09PSAnYXInID8gJ2ZvbnQtYXJhYmljLWhlYWRpbmdzJyA6ICdmb250LWVuZ2xpc2gtcHJpbWFyeSdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2xlYWRlcnNbY3VycmVudEluZGV4XS5wb3NpdGlvbn1cbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPXtgdGV4dC14bCBtZDp0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtcHVyZS13aGl0ZSBtYi00ICR7XG4gICAgICAgICAgICAgICAgICAgIGxvY2FsZSA9PT0gJ2FyJyA/ICdmb250LWFyYWJpYy1zZWNvbmRhcnknIDogJ2ZvbnQtZW5nbGlzaC1zZWNvbmRhcnknXG4gICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgIHtsZWFkZXJzW2N1cnJlbnRJbmRleF0ubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LWxpZ2h0LWdyYXkgbGVhZGluZy1yZWxheGVkIHRleHQtbGcgJHtcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxlID09PSAnYXInID8gJ2ZvbnQtYXJhYmljLXNlY29uZGFyeScgOiAnZm9udC1lbmdsaXNoLXNlY29uZGFyeSdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2xlYWRlcnNbY3VycmVudEluZGV4XS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG5cbiAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIEFycm93cyAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17cHJldlNsaWRlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTQgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBiZy1taWxpdGFyeS1ibGFjay83MCBob3ZlcjpiZy1taWxpdGFyeS1ncmVlbi83MCB0ZXh0LXB1cmUtd2hpdGUgcC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgbWlsaXRhcnktZ2xvd1wiXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2LPYp9io2YInIDogJ1ByZXZpb3VzJ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPENoZXZyb25MZWZ0SWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtuZXh0U2xpZGV9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTQgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiBiZy1taWxpdGFyeS1ibGFjay83MCBob3ZlcjpiZy1taWxpdGFyeS1ncmVlbi83MCB0ZXh0LXB1cmUtd2hpdGUgcC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgbWlsaXRhcnktZ2xvd1wiXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mE2KrYp9mE2YonIDogJ05leHQnfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0SWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIERvdHMgSW5kaWNhdG9yICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtdC02IHNwYWNlLXgtMiBydGw6c3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICB7bGVhZGVycy5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGdvVG9TbGlkZShpbmRleCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy0zIGgtMyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICBpbmRleCA9PT0gY3VycmVudEluZGV4XG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLW1pbGl0YXJ5LWdyZWVuIG1pbGl0YXJ5LWdsb3cnXG4gICAgICAgICAgICAgICAgICAgIDogJ2JnLXNtb2tlLWdyYXkgaG92ZXI6YmctYnJpZ2h0LWdyZWVuLzUwJ1xuICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2Ake2xvY2FsZSA9PT0gJ2FyJyA/ICfYp9mG2KrZgtmEINil2YTZiSDYp9mE2LTYsdmK2K3YqScgOiAnR28gdG8gc2xpZGUnfSAke2luZGV4ICsgMX1gfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQXV0by1wbGF5IENvbnRyb2wgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIG10LTRcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNBdXRvUGxheWluZyghaXNBdXRvUGxheWluZyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTQgcHktMiByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgaXNBdXRvUGxheWluZ1xuICAgICAgICAgICAgICAgICAgPyAnYmctbWlsaXRhcnktZ3JlZW4gdGV4dC1wdXJlLXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgOiAnYmctc21va2UtZ3JheSB0ZXh0LWxpZ2h0LWdyYXkgaG92ZXI6YmctYnJpZ2h0LWdyZWVuLzUwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzQXV0b1BsYXlpbmcgXG4gICAgICAgICAgICAgICAgPyAobG9jYWxlID09PSAnYXInID8gJ9il2YrZgtin2YEg2KfZhNiq2LTYutmK2YQg2KfZhNiq2YTZgtin2KbZiicgOiAnUGF1c2UgQXV0by1wbGF5JylcbiAgICAgICAgICAgICAgICA6IChsb2NhbGUgPT09ICdhcicgPyAn2KrYtNi62YrZhCDYqtmE2YLYp9im2YonIDogJ0F1dG8tcGxheScpXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVRyYW5zbGF0aW9ucyIsInVzZUxvY2FsZSIsIm1vdGlvbiIsIkFuaW1hdGVQcmVzZW5jZSIsIkNoZXZyb25MZWZ0SWNvbiIsIkNoZXZyb25SaWdodEljb24iLCJMZWFkZXJzaGlwQ2Fyb3VzZWwiLCJ0IiwibG9jYWxlIiwiY3VycmVudEluZGV4Iiwic2V0Q3VycmVudEluZGV4IiwiaXNBdXRvUGxheWluZyIsInNldElzQXV0b1BsYXlpbmciLCJsZWFkZXJzIiwiaWQiLCJwb3NpdGlvbiIsIm5hbWUiLCJpbWFnZSIsImRlc2NyaXB0aW9uIiwiaW50ZXJ2YWwiLCJzZXRJbnRlcnZhbCIsInByZXYiLCJsZW5ndGgiLCJjbGVhckludGVydmFsIiwibmV4dFNsaWRlIiwicHJldlNsaWRlIiwiZ29Ub1NsaWRlIiwiaW5kZXgiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDIiLCJwIiwibW9kZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieCIsImFuaW1hdGUiLCJleGl0IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSIsInNwYW4iLCJjaGFyQXQiLCJoMyIsImg0IiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJtYXAiLCJfIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/sections/LeadershipCarousel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sections/StatisticsDashboard.tsx":
/*!*****************************************************!*\
  !*** ./components/sections/StatisticsDashboard.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatisticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AcademicCapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/TrophyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AcademicCapIcon,ClockIcon,CogIcon,GlobeAltIcon,ShieldCheckIcon,StarIcon,TrophyIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst iconMap = {\n    0: _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    1: _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    2: _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    3: _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n};\nfunction AnimatedCounter({ value, duration = 2000 }) {\n    const [displayValue, setDisplayValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useInView)(ref, {\n        once: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            if (!isInView) return;\n            // Extract numeric part from value\n            const numericMatch = value.match(/\\d+/);\n            if (!numericMatch) {\n                setDisplayValue(value);\n                return;\n            }\n            const targetNumber = parseInt(numericMatch[0]);\n            const suffix = value.replace(numericMatch[0], '');\n            let startTime;\n            let animationFrame;\n            const animate = {\n                \"AnimatedCounter.useEffect.animate\": (currentTime)=>{\n                    if (!startTime) startTime = currentTime;\n                    const progress = Math.min((currentTime - startTime) / duration, 1);\n                    // Easing function for smooth animation\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    const currentNumber = Math.floor(targetNumber * easeOutQuart);\n                    setDisplayValue(currentNumber + suffix);\n                    if (progress < 1) {\n                        animationFrame = requestAnimationFrame(animate);\n                    }\n                }\n            }[\"AnimatedCounter.useEffect.animate\"];\n            animationFrame = requestAnimationFrame(animate);\n            return ({\n                \"AnimatedCounter.useEffect\": ()=>{\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                }\n            })[\"AnimatedCounter.useEffect\"];\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        isInView,\n        value,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        children: displayValue\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n        lineNumber: 75,\n        columnNumber: 10\n    }, this);\n}\nfunction StatisticsDashboard() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)('about.statistics');\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useLocale)();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useInView)(containerRef, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const statistics = t.raw('items');\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const cardVariants = {\n        hidden: {\n            opacity: 0,\n            y: 60,\n            scale: 0.8,\n            rotateX: -15\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            rotateX: 0\n        }\n    };\n    const glowVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: [\n                0.3,\n                0.7,\n                0.3\n            ]\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-military-black/50 backdrop-blur-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6\n                            },\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-8 h-8 text-military-green mr-3 rtl:mr-0 rtl:ml-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: `text-3xl md:text-4xl font-bold text-pure-white ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: t('title')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: isInView ? {\n                                opacity: 1,\n                                y: 0\n                            } : {},\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.2\n                            },\n                            className: `text-light-gray text-lg ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                            children: t('subtitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    ref: containerRef,\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: statistics.map((stat, index)=>{\n                        const IconComponent = iconMap[index] || _barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            variants: cardVariants,\n                            transition: {\n                                duration: 0.8,\n                                ease: \"easeOut\"\n                            },\n                            whileHover: {\n                                scale: 1.05,\n                                y: -10,\n                                transition: {\n                                    duration: 0.3\n                                }\n                            },\n                            className: \"relative group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    variants: glowVariants,\n                                    transition: {\n                                        duration: 2,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"absolute inset-0 bg-military-green/20 rounded-lg blur-xl\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-steel-gray/70 backdrop-blur-sm rounded-lg p-8 border border-smoke-gray/30 military-glow hover:military-glow-strong transition-all duration-300 h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            whileHover: {\n                                                rotate: 360\n                                            },\n                                            transition: {\n                                                duration: 0.6\n                                            },\n                                            className: \"flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-gradient-to-br from-military-green to-bright-green rounded-full flex items-center justify-center military-glow-strong\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"w-8 h-8 text-pure-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    className: `text-4xl md:text-5xl font-bold text-bright-green mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                        value: stat.number,\n                                                        duration: 2000 + index * 200\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: `text-xl font-semibold text-pure-white mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-light-gray text-center leading-relaxed ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                            children: stat.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 rtl:right-auto rtl:left-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                animate: {\n                                                    rotate: [\n                                                        0,\n                                                        360\n                                                    ],\n                                                    scale: [\n                                                        1,\n                                                        1.1,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 4,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                className: \"w-3 h-3 bg-military-green/50 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 left-0 right-0 h-1 bg-smoke-gray/30 rounded-b-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: isInView ? {\n                                                    width: '100%'\n                                                } : {},\n                                                transition: {\n                                                    duration: 1.5,\n                                                    delay: 0.5 + index * 0.2,\n                                                    ease: \"easeOut\"\n                                                },\n                                                className: \"h-full bg-gradient-to-r from-military-green to-bright-green\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 40\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {},\n                    transition: {\n                        duration: 0.8,\n                        delay: 1.2\n                    },\n                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-6 h-6 text-pure-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-bright-green mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: locale === 'ar' ? 'التميز التشغيلي' : 'Operational Excellence'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-light-gray text-sm ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                    children: locale === 'ar' ? 'أعلى معايير الأداء والكفاءة' : 'Highest standards of performance and efficiency'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-6 h-6 text-pure-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-bright-green mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: locale === 'ar' ? 'الشراكات الاستراتيجية' : 'Strategic Partnerships'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-light-gray text-sm ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                    children: locale === 'ar' ? 'تعاون دولي وإقليمي فعال' : 'Effective international and regional cooperation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-military-green rounded-full flex items-center justify-center mx-auto mb-4 military-glow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AcademicCapIcon_ClockIcon_CogIcon_GlobeAltIcon_ShieldCheckIcon_StarIcon_TrophyIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-6 h-6 text-pure-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: `text-lg font-semibold text-bright-green mb-2 ${locale === 'ar' ? 'font-arabic-headings' : 'font-english-primary'}`,\n                                    children: locale === 'ar' ? 'التركيز على الابتكار' : 'Innovation Focus'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: `text-light-gray text-sm ${locale === 'ar' ? 'font-arabic-secondary' : 'font-english-secondary'}`,\n                                    children: locale === 'ar' ? 'تطوير مستمر للقدرات والتقنيات' : 'Continuous development of capabilities and technologies'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\defence minister of syria\\\\syrian-defense-ministry\\\\components\\\\sections\\\\StatisticsDashboard.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sections/StatisticsDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2RlZmVuY2UlMjBtaW5pc3RlciUyMG9mJTIwc3lyaWElNUMlNUNzeXJpYW4tZGVmZW5zZS1taW5pc3RyeSU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2FtZXJrJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2RlZmVuY2UlMjBtaW5pc3RlciUyMG9mJTIwc3lyaWElNUMlNUNzeXJpYW4tZGVmZW5zZS1taW5pc3RyeSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtaW50bCU1QyU1Q2Rpc3QlNUMlNUNlc20lNUMlNUNkZXZlbG9wbWVudCU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3UUFBdVAiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxhbWVya1xcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxkZWZlbmNlIG1pbmlzdGVyIG9mIHN5cmlhXFxcXHN5cmlhbi1kZWZlbnNlLW1pbmlzdHJ5XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0LWludGxcXFxcZGlzdFxcXFxlc21cXFxcZGV2ZWxvcG1lbnRcXFxcc2hhcmVkXFxcXE5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Footer.tsx */ \"(ssr)/./components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/layout/Navigation.tsx */ \"(ssr)/./components/layout/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/HistoricalTimeline.tsx */ \"(ssr)/./components/sections/HistoricalTimeline.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/LeadershipCarousel.tsx */ \"(ssr)/./components/sections/LeadershipCarousel.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/sections/StatisticsDashboard.tsx */ \"(ssr)/./components/sections/StatisticsDashboard.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CHistoricalTimeline.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CLeadershipCarousel.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Ccomponents%5C%5Csections%5C%5CStatisticsDashboard.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cdevelopment%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Camerk%5C%5CDocuments%5C%5Caugment-projects%5C%5Cdefence%20minister%20of%20syria%5C%5Csyrian-defense-ministry%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@swc","vendor-chunks/@formatjs","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/next-intl","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F%5Blocale%5D%2Fabout%2Fpage&page=%2F%5Blocale%5D%2Fabout%2Fpage&appPaths=%2F%5Blocale%5D%2Fabout%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Camerk%5CDocuments%5Caugment-projects%5Cdefence%20minister%20of%20syria%5Csyrian-defense-ministry&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();